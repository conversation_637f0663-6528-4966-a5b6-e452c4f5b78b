local QBCore = exports['qb-core']:GetCoreObject()

local accountIdCache = {}

local playersWithTagsActive = {}

function GetAllPlayerGroups()
    local players = {}
    local qbPlayers = QBCore.Functions.GetQBPlayers()

    for playerId, Player in pairs(qbPlayers) do
        if Player then
            players[tostring(playerId)] = Player.PlayerData.metadata.group or 'user'
        end
    end

    return players
end

function GetAllAccountIds()
    return accountIdCache
end

function LoadAccountIdForPlayer(playerId, callback)
    local Player = QBCore.Functions.GetPlayer(playerId)
    if not Player then
        if callback then callback(playerId) end
        return
    end

    print('[TAGS] Loading account_id for player ' .. playerId .. ' with identifier: ' .. Player.PlayerData.citizenid)

    MySQL.Async.fetchScalar(
        'SELECT account_id FROM users WHERE citizenid = @citizenid',
        { ['@citizenid'] = Player.PlayerData.citizenid },
        function(accountId)
            if accountId then
                accountIdCache[playerId] = tonumber(accountId)
                print('[TAGS] SUCCESS: Loaded account_id ' .. accountId .. ' for player ' .. playerId)
            else
                accountIdCache[playerId] = playerId
                print('[TAGS] WARNING: No account_id found for player ' .. playerId .. ', using server ID as fallback')
            end

            if callback then callback(accountId or playerId) end
        end
    )
end

RegisterServerEvent('tags:requestPlayerGroups')
AddEventHandler('tags:requestPlayerGroups', function()
    local source = source
    local playerGroups = GetAllPlayerGroups()
    local playerAccountIds = GetAllAccountIds()

    print('[TAGS] Sending player groups and account IDs to client ' .. source)
    TriggerClientEvent('tags:receivePlayerGroups', source, playerGroups, playerAccountIds)
end)

RegisterServerEvent('tags:requestActiveTagPlayers')
AddEventHandler('tags:requestActiveTagPlayers', function()
    local source = source
    print('[TAGS] Sending active tag players to client ' .. source .. ': ' .. json.encode(playersWithTagsActive))
    TriggerClientEvent('tags:receiveActiveTagPlayers', source, playersWithTagsActive)
end)

RegisterServerEvent('tags:playerToggled')
AddEventHandler('tags:playerToggled', function(isActive)
    local source = source
    local serverId = tostring(source)

    if isActive then
        playersWithTagsActive[serverId] = true
        print('[TAGS] Player ' .. source .. ' enabled nametags')
    else
        playersWithTagsActive[serverId] = nil
        print('[TAGS] Player ' .. source .. ' disabled nametags')
    end

    for playerIdStr, isTagsActive in pairs(playersWithTagsActive) do
        if isTagsActive then
            local playerId = tonumber(playerIdStr)
            if playerId then
                TriggerClientEvent('tags:receiveActiveTagPlayers', playerId, playersWithTagsActive)
                print('[TAGS] Sent updated active tag players to player ' .. playerId)
            end
        end
    end

    print('[TAGS] Updated active tag players: ' .. json.encode(playersWithTagsActive))
end)

RegisterNetEvent('QBCore:Server:PlayerLoaded', function(Player)
    local playerId = Player.PlayerData.source
    print('[TAGS] Player loaded: ' .. playerId .. ', Identifier: ' .. Player.PlayerData.citizenid)

    LoadAccountIdForPlayer(playerId, function(accountId)
        print('[TAGS] Account ID loaded for player ' .. playerId .. ': ' .. tostring(accountId))

        SetTimeout(1000, function()
            local playerGroups = GetAllPlayerGroups()
            local playerAccountIds = GetAllAccountIds()

            print('[TAGS] Sending to clients - Groups: ' .. json.encode(playerGroups))
            print('[TAGS] Sending to clients - Account IDs: ' .. json.encode(playerAccountIds))

            for playerIdStr, isTagsActive in pairs(playersWithTagsActive) do
                if isTagsActive then
                    local activePlayerId = tonumber(playerIdStr)
                    if activePlayerId then
                        TriggerClientEvent('tags:receivePlayerGroups', activePlayerId, playerGroups, playerAccountIds)
                    end
                end
            end

            TriggerClientEvent('tags:receivePlayerGroups', -1, playerGroups, playerAccountIds)
            print('[TAGS] Player ' .. playerId .. ' loaded, updated clients with nametags active')
        end)
    end)
end)

AddEventHandler('playerDropped', function()
    local source = source
    local serverId = tostring(source)

    accountIdCache[source] = nil
    playersWithTagsActive[serverId] = nil
    print('[TAGS] Player ' .. source .. ' dropped, removed from caches')

    Wait(1000)

    local playerGroups = GetAllPlayerGroups()
    local playerAccountIds = GetAllAccountIds()

    for playerIdStr, isTagsActive in pairs(playersWithTagsActive) do
        if isTagsActive then
            local activePlayerId = tonumber(playerIdStr)
            if activePlayerId then
                TriggerClientEvent('tags:receivePlayerGroups', activePlayerId, playerGroups, playerAccountIds)
                TriggerClientEvent('tags:receiveActiveTagPlayers', activePlayerId, playersWithTagsActive)
            end
        end
    end
    print('[TAGS] Updated clients with active nametags after player drop')
end)


AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end

    print('[TAGS] Resource starting - loading account IDs for existing players')

    SetTimeout(2000, function()
        local players = GetPlayers()
        local loadedCount = 0
        local totalPlayers = #players

        if totalPlayers == 0 then
            print('[TAGS] No players online, resource ready')
            return
        end

        print('[TAGS] Loading account IDs for ' .. totalPlayers .. ' existing players')

        for _, playerId in ipairs(players) do
            local pid = tonumber(playerId)
            LoadAccountIdForPlayer(pid, function()
                loadedCount = loadedCount + 1
                print('[TAGS] Loaded ' .. loadedCount .. '/' .. totalPlayers .. ' account IDs')

                if loadedCount >= totalPlayers then
                    local playerGroups = GetAllPlayerGroups()
                    local playerAccountIds = GetAllAccountIds()

                    print('[TAGS] All account IDs loaded - Groups: ' .. json.encode(playerGroups))
                    print('[TAGS] All account IDs loaded - Account IDs: ' .. json.encode(playerAccountIds))

                    TriggerClientEvent('tags:receivePlayerGroups', -1, playerGroups, playerAccountIds)
                    print('[TAGS] Sent initial data to all clients')
                end
            end)
        end
    end)
end)

print('[TAGS] Server script loaded successfully')
