local function getPlayerIdentifier(src)
    local identifiers = GetPlayerIdentifiers(src)
    for _, id in pairs(identifiers) do
        if string.find(id, "license:") then
            return id:gsub("license:", "")
        end
    end
    return nil
end

local function getCooldownFromDB(identifier)
    local result = MySQL.Sync.fetchAll('SELECT cooldown FROM player_cooldowns WHERE identifier = ?', { identifier })
    return (result and result[1] and result[1].cooldown) or 0
end

local function setCooldownInDB(identifier, cooldownTime)
    MySQL.Async.execute('INSERT INTO player_cooldowns (identifier, cooldown) VALUES (?, ?) ON DUPLICATE KEY UPDATE cooldown = ?', 
        { identifier, cooldownTime, cooldownTime })
end

lib.callback.register('base_player:checkCD', function(source)
    local identifier = getPlayerIdentifier(source)
    if not identifier then return 0 end

    local cooldown = getCooldownFromDB(identifier)
    local currentTime = os.time()

    return cooldown > currentTime and (cooldown - currentTime) or 0
end)

lib.callback.register('base_player:getJob', function(source, targetServerId)
    local targetPlayer = exports.qbx_core:GetPlayer(targetServerId)
    if targetPlayer then
        return targetPlayer.job.name
    end
    return nil
end)

lib.callback.register('base_player:RobberyCD', function(source, isDead)
    local identifier = getPlayerIdentifier(source)
    if not identifier then return end

    local cooldownDuration = isDead and 60 * 60 or 15 * 60
    local cooldownTime = os.time() + cooldownDuration

    setCooldownInDB(identifier, cooldownTime)
end)

lib.callback.register('base_player:getPlaytime', function(source)
    local playtime = exports['base_player']:GetPlayerPlaytime(source)
    return playtime or 0
end)