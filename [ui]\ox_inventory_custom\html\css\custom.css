/* Custom ox_inventory styling to match NoFX design */
/* Based on the provided image with dark theme and teal accents */

/* Main inventory container styling */
.inventory-container,
.inventory-wrapper,
.inventory-grid,
.inventory-main {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(10px) !important;
}

/* Inventory header styling */
.inventory-header,
.inventory-title,
.header {
    background: linear-gradient(90deg, #2d2d2d 0%, #3a3a3a 100%) !important;
    color: #4a9b9b !important;
    border-bottom: 1px solid #4a9b9b !important;
    padding: 12px 16px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

/* Weight/capacity display */
.inventory-weight,
.weight-display,
.capacity {
    background: rgba(74, 155, 155, 0.1) !important;
    color: #4a9b9b !important;
    border: 1px solid #4a9b9b !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

/* Individual inventory slots */
.inventory-slot,
.slot,
.item-slot {
    background: rgba(45, 45, 45, 0.8) !important;
    border: 1px solid #404040 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    width: 64px !important;
    height: 64px !important;
    margin: 2px !important;
}

/* Slot hover effects */
.inventory-slot:hover,
.slot:hover,
.item-slot:hover {
    border-color: #4a9b9b !important;
    background: rgba(74, 155, 155, 0.1) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 0 12px rgba(74, 155, 155, 0.3) !important;
}

/* Item images in slots */
.inventory-slot img,
.slot img,
.item-slot img,
.item-image {
    width: 48px !important;
    height: 48px !important;
    object-fit: contain !important;
    filter: brightness(1.1) contrast(1.1) !important;
    border-radius: 4px !important;
}

/* Item quantity/count display */
.item-count,
.item-quantity,
.quantity {
    position: absolute !important;
    bottom: 2px !important;
    right: 2px !important;
    background: rgba(74, 155, 155, 0.9) !important;
    color: #ffffff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 1px 4px !important;
    border-radius: 3px !important;
    min-width: 16px !important;
    text-align: center !important;
}

/* Item durability/condition bars */
.durability-bar,
.condition-bar,
.item-durability {
    position: absolute !important;
    bottom: 2px !important;
    left: 2px !important;
    right: 2px !important;
    height: 3px !important;
    background: rgba(0, 0, 0, 0.5) !important;
    border-radius: 2px !important;
    overflow: hidden !important;
}

.durability-fill,
.condition-fill {
    height: 100% !important;
    background: linear-gradient(90deg, #ff4444 0%, #ffaa00 50%, #4a9b9b 100%) !important;
    transition: width 0.3s ease !important;
}

/* Inventory grid layout */
.inventory-grid {
    display: grid !important;
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 4px !important;
    padding: 16px !important;
    max-width: 600px !important;
    max-height: 500px !important;
}

/* Left inventory (player) */
.inventory-left,
.player-inventory {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
    margin-right: 16px !important;
}

/* Right inventory (external) */
.inventory-right,
.external-inventory,
.secondary-inventory {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
    margin-left: 16px !important;
}

/* Inventory positioning to match image */
.inventory-wrapper {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    display: flex !important;
    gap: 20px !important;
    z-index: 1000 !important;
}

/* Scrollbar styling */
.inventory-container::-webkit-scrollbar,
.inventory-grid::-webkit-scrollbar {
    width: 8px !important;
}

.inventory-container::-webkit-scrollbar-track,
.inventory-grid::-webkit-scrollbar-track {
    background: rgba(45, 45, 45, 0.5) !important;
    border-radius: 4px !important;
}

.inventory-container::-webkit-scrollbar-thumb,
.inventory-grid::-webkit-scrollbar-thumb {
    background: #4a9b9b !important;
    border-radius: 4px !important;
}

.inventory-container::-webkit-scrollbar-thumb:hover,
.inventory-grid::-webkit-scrollbar-thumb:hover {
    background: #5bb5b5 !important;
}

/* Item tooltip styling */
.item-tooltip,
.tooltip {
    background: rgba(26, 26, 26, 0.95) !important;
    border: 1px solid #4a9b9b !important;
    border-radius: 6px !important;
    color: #ffffff !important;
    padding: 12px !important;
    font-size: 12px !important;
    max-width: 250px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(8px) !important;
}

/* Close button styling */
.close-button,
.inventory-close {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    background: rgba(255, 68, 68, 0.8) !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 50% !important;
    width: 24px !important;
    height: 24px !important;
    cursor: pointer !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
}

.close-button:hover,
.inventory-close:hover {
    background: rgba(255, 68, 68, 1) !important;
    transform: scale(1.1) !important;
}

/* Animation for inventory opening */
@keyframes inventoryFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.inventory-wrapper {
    animation: inventoryFadeIn 0.3s ease-out !important;
}

/* Responsive adjustments */
@media (max-width: 1920px) {
    .inventory-grid {
        max-width: 550px !important;
        max-height: 450px !important;
    }
}

@media (max-width: 1366px) {
    .inventory-grid {
        max-width: 500px !important;
        max-height: 400px !important;
    }
    
    .inventory-slot,
    .slot,
    .item-slot {
        width: 56px !important;
        height: 56px !important;
    }
}

/* Additional specific selectors for different ox_inventory versions */
.ox_inventory .inventory,
.ox_inventory .container,
.ox_inventory .main {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
}

/* Ensure the styling applies to all possible inventory elements */
[class*="inventory"],
[class*="slot"],
[class*="item"] {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}
