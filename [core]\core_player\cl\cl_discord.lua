local QBCore = exports['qb-core']:GetCoreObject()
CreateThread(function()
    while true do

	SetDiscordAppId(1251733536889770024)
	SetDiscordRichPresenceAsset('logo')

        QBCore.Functions.TriggerCallback('smallresources:server:GetCurrentPlayers', function(result)
            SetRichPresence('Players: '..result..'/130')
        end)

        SetDiscordRichPresenceAction(0, "Join Arctic Gaming!", "https://cfx.re/join/9386de")
        SetDiscordRichPresenceAction(1, "Join The Arctic Gaming Discord!", "https://discord.gg/2cjeEgBwHK") 

	Wait(1000)
    end
end)
