local QBCore = exports['qb-core']:GetCoreObject()

RegisterNetEvent('base_player:login')
AddEventHandler('base_player:login', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    local license = Player.PlayerData.license
    if not license then
        print("Player has no license.")
        return
    end

    MySQL.query('SELECT account_id FROM player_accounts WHERE license = ?', {license}, function(result)
        if result[1] then
            local accountID = result[1].account_id
            TriggerClientEvent('base_player:showID', src, accountID)
        else
            MySQL.insert('INSERT INTO player_accounts (license) VALUES (?)', {license}, function(insertID)
                TriggerClientEvent('base_player:showID', src, insertID)
            end)
        end
    end)
end)

lib.addCommand('lookupid', {
    help = 'Lookup a player\'s license using an account ID',
    restricted = 'admin'
}, function(source, args, raw)
    TriggerClientEvent('qb-accountid:openLookupMenu', source)
end)

lib.callback.register('qb-accountid:lookupLicense', function(source, accountID)
    if not accountID or tonumber(accountID) == nil then return nil end

    local result = MySQL.query.await('SELECT license FROM player_accounts WHERE account_id = ?', {tonumber(accountID)})
    if result and result[1] then
        return result[1].license
    else
        return nil
    end
end)
