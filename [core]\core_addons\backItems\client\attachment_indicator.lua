-- Attachment Indicator System for Inventory UI
-- This script provides functionality to show attachment indicators in the inventory

local Utils = require 'backItems.imports.utils'

-- Function to create attachment indicator text for UI
local function getAttachmentIndicatorText(item)
    if not item then return "" end
    
    local attachmentCount = Utils.getAttachmentCount(item)
    if attachmentCount > 0 then
        return string.format("📎 %d", attachmentCount) -- Paperclip emoji with count
    end
    
    return ""
end

-- Function to get attachment indicator with detailed info
local function getDetailedAttachmentInfo(item)
    if not item then return nil end
    
    local attachments = Utils.getAttachmentInfo(item)
    if #attachments > 0 then
        return {
            hasAttachments = true,
            count = #attachments,
            attachments = attachments,
            indicator = string.format("📎 %d", #attachments)
        }
    end
    
    return {
        hasAttachments = false,
        count = 0,
        attachments = {},
        indicator = ""
    }
end

-- Export functions for inventory UI integration
exports('getAttachmentIndicatorText', getAttachmentIndicatorText)
exports('getDetailedAttachmentInfo', getDetailedAttachmentInfo)

-- Event handler for inventory UI to request attachment info
RegisterNetEvent('backItems:requestAttachmentInfo', function(slot)
    local item = InvCache and InvCache[slot]
    local info = getDetailedAttachmentInfo(item)
    TriggerEvent('backItems:attachmentInfoResponse', slot, info)
end)



-- Function to create a visual indicator overlay (for custom inventory UIs)
local function createAttachmentOverlay(x, y, width, height, count)
    -- This would be used by custom inventory UIs to draw attachment indicators
    -- Example implementation for drawing text overlay
    
    if count > 0 then
        local text = string.format("📎%d", count)
        
        -- Set text properties
        SetTextFont(0)
        SetTextProportional(1)
        SetTextScale(0.25, 0.25)
        SetTextColour(255, 255, 255, 255)
        SetTextDropShadow(0, 0, 0, 0, 255)
        SetTextEdge(1, 0, 0, 0, 255)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        AddTextComponentString(text)
        
        -- Draw text at specified position (top-right corner of item slot)
        DrawText(x + width - 0.03, y + 0.005)
    end
end

exports('createAttachmentOverlay', createAttachmentOverlay)

-- Integration example for ox_inventory (if needed)
-- This shows how the attachment system could be integrated with ox_inventory
local function integrateWithOxInventory()
    -- Hook into ox_inventory events to provide attachment info
    AddEventHandler('ox_inventory:itemTooltip', function(item, tooltip)
        if item and Utils.hasAttachments(item) then
            local attachments = Utils.getAttachmentInfo(item)
            tooltip[#tooltip + 1] = string.format("^3Attachments: %d^7", #attachments)
            
            for i, attachment in ipairs(attachments) do
                tooltip[#tooltip + 1] = string.format("^8- %s^7", attachment)
            end
        end
    end)
end

-- Initialize integration when resource starts
CreateThread(function()
    Wait(1000) -- Wait for other resources to load
    integrateWithOxInventory()
end)
