<?xml version="1.0" encoding="UTF-8"?>
<CDoorTuningFile>
  <NamedTuningArray>
    <Item>
      <Name>BarrierArmCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-3.000000" z="-3.500000" />
        <Flags>DontCloseWhenTouched AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPVehicleWithPedsOnly</Flags>
        <AutoOpenRadiusModifier value="5.000000" />
        <AutoOpenRate value="0.750000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.000000" y="-3.000000" z="-1.500000" w="-340282303855321090000000000000000000.000000" />
          <max x="3.000000" y="3.000000" z="4.250000" w="-340282303855321090000000000000000000.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="true" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="0.010000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>ChopShopGarageCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-2.600000" z="-2.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="0.750000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-6.250000" y="-19.975000" z="0.000000" w="0.000000" />
          <max x="2.975000" y="11.125000" z="3.350000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>CultistGate</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="20.000000" />
        <WeaponImpulseMultiplier value="0.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultBarrierArm</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPVehicleWithPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.500000" />
        <AutoOpenRate value="0.750000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="true" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultGarage</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="-2.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.500000" />
        <AutoOpenRate value="0.275000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultGarageBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-2.600000" z="-2.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="0.750000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultGarageCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="-2.000000" />
        <Flags>DontCloseWhenTouched</Flags>
        <AutoOpenRadiusModifier value="1.500000" />
        <AutoOpenRate value="0.275000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-2.000000" y="-5.950000" z="-1.000000" w="0.000000" />
          <max x="1.900000" y="5.950000" z="3.675000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultSlidingHorizontal</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="2.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultSlidingVertical</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultStandard</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>DelayDoorClosingForPlayer</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>DefaultStandardLatchShut</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>DelayDoorClosingForPlayer</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="true" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>NoRotationLimitStandardLatchShut</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>DelayDoorClosingForPlayer</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="true" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="89.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FBISecurityGate</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="2.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-0.125000" y="-1.725000" z="-0.495000" w="0.000000" />
          <max x="0.525000" y="1.725000" z="0.650000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FenceGate</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>DontCloseWhenTouched IgnoreOpenDoorTaskEdgeLerp</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.250000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="true" />
        <BreakingImpulse value="10000.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="0.400000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FragBarrierArm</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPVehicleWithPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.500000" />
        <AutoOpenRate value="0.750000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FragBarrierArmCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPVehicleWithPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.500000" />
        <AutoOpenRate value="0.750000" />
        <AutoOpenCosineAngleBetweenThreshold value="0.707000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-2.575000" y="-12.000000" z="-1.275000" w="0.000000" />
          <max x="3.300000" y="0.000000" z="4.150000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="true" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FranklinGarage2</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="-2.000000" />
        <Flags>AutoOpensForSPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.500000" />
        <AutoOpenRate value="0.275000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.800000" y="-6.300000" z="0.000000" w="0.000000" />
          <max x="3.800000" y="8.800000" z="6.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FranklinGarageCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-3.500000" z="-2.000000" />
        <Flags>AutoOpensForSPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="0.750000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-2.450000" y="-5.000000" z="0.000000" w="0.000000" />
          <max x="1.800000" y="11.000000" z="3.350000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>GangGarageCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-3.500000" z="-2.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="0.750000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.000000" y="-1.000000" z="0.000000" w="0.000000" />
          <max x="3.000000" y="11.000000" z="3.350000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>HeavyVaultDoor</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>DontCloseWhenTouched IgnoreOpenDoorTaskEdgeLerp</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="true" />
        <MassMultiplier value="15.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="170.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenNegDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>HeavyWoodDoor</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>IgnoreOpenDoorTaskEdgeLerp</Flags>
        <AutoOpenRadiusModifier value="1.200000" />
        <AutoOpenRate value="0.200000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>LargeFenceGate</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>DelayDoorClosingForPlayer IgnoreOpenDoorTaskEdgeLerp</Flags>
        <AutoOpenRadiusModifier value="1.200000" />
        <AutoOpenRate value="0.200000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="40000.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="0.100000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>LargeGate</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>IgnoreOpenDoorTaskEdgeLerp</Flags>
        <AutoOpenRadiusModifier value="0.985000" />
        <AutoOpenRate value="1.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="10.000000" />
        <WeaponImpulseMultiplier value="0.100000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="0.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>MichaelGarageCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-2.600000" z="-2.000000" />
        <Flags>AutoOpensForSPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="0.750000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.250000" y="-4.175000" z="0.000000" w="0.000000" />
          <max x="3.000000" y="11.225000" z="3.350000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>ReduceOpenGarage</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="-2.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="0.635000" />
        <AutoOpenRate value="0.275000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SlidingHorizontalExtendedRange</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.800000" />
        <AutoOpenRate value="2.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SlidingHorizontalSPVehiclesAndMPPlayerPeds</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="4.000000" />
        <AutoOpenRate value="1.50000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="4.000000" y="4.000000" z="0.000000" w="0.000000" />
          <max x="4.000000" y="4.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SlidingHorizontalVehiclesOnly</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPVehicleWithPedsOnly AutoOpensForMPVehicleWithPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SprayDoors</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="1.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.009900" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>StandardHeavy</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="15.000000" />
        <WeaponImpulseMultiplier value="0.010000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>TollBarrierArmCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-3.000000" z="-3.500000" />
        <Flags>DontCloseWhenTouched AutoOpensForAllVehicles</Flags>
        <AutoOpenRadiusModifier value="5.000000" />
        <AutoOpenRate value="0.750000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.000000" y="-3.000000" z="-1.500000" w="-340282303855321090000000000000000000.000000" />
          <max x="3.000000" y="3.000000" z="4.250000" w="-340282303855321090000000000000000000.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="true" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="0.010000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>TrevorGarageCustomBox</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-3.500000" z="-2.000000" />
        <Flags>AutoOpensForSPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="0.750000" />
        <AutoOpenRate value="0.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-2.750000" y="-5.000000" z="0.000000" w="0.000000" />
          <max x="1.850000" y="15.500000" z="3.350000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>TowTruckYardSlidingHorizontal</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPPlayerPedsOnly AutoOpensForMPPlayerPedsOnly</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="2.250000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.000000" y="-6.250000" z="-1.500000" w="0.000000" />
          <max x="3.000000" y="8.000000" z="3.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SlidingHorizontalPoliceSPVehiclesAndMPPlayerPeds</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags>AutoOpensForSPPlayerPedsOnly AutoOpensForMPPlayerPedsOnly AutoOpensForLawEnforcement</Flags>
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="2.250000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="-3.000000" y="-6.250000" z="-1.500000" w="0.000000" />
          <max x="3.000000" y="8.000000" z="3.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SupermodGarage</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-1.675000" z="-2.450000" />
        <Flags />
        <AutoOpenRadiusModifier value="2.763000" />
        <AutoOpenRate value="1.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>SupermodGarage2</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="-1.675000" z="-2.450000" />
        <Flags />
        <AutoOpenRadiusModifier value="2.763000" />
        <AutoOpenRate value="1.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="true" />
        <TriggerBoxMinMax>
          <min x="3.000000" y="3.000000" z="3.000000" w="0.000000" />
          <max x="5.000000" y="5.000000" z="5.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FatGarage</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.363000" />
        <AutoOpenRate value="2.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>BollardPolice</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.563000" />
        <AutoOpenRate value="2.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FatFacgate</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.000000" />
        <AutoOpenRate value="1.000000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>VPDGarage</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="0.900000" />
        <AutoOpenRate value="0.300000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="true" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>as_customs_reds_auto_gar</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="35.935000" />
        <AutoOpenRate value="1.00000" />
        <AutoOpenCosineAngleBetweenThreshold value="-1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="false" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
    <Item>
      <Name>FatGarageHayes</Name>
      <Tuning>
        <AutoOpenVolumeOffset x="0.000000" y="0.000000" z="0.000000" />
        <Flags />
        <AutoOpenRadiusModifier value="1.363000" />
        <AutoOpenRate value="1.500000" />
        <AutoOpenCosineAngleBetweenThreshold value="1.000000" />
        <AutoOpenCloseRateTaper value="false" />
        <UseAutoOpenTriggerBox value="true" />
        <CustomTriggerBox value="false" />
        <TriggerBoxMinMax>
          <min x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
          <max x="0.000000" y="0.000000" z="0.000000" w="0.000000" />
        </TriggerBoxMinMax>
        <BreakableByVehicle value="false" />
        <BreakingImpulse value="0.000000" />
        <ShouldLatchShut value="false" />
        <MassMultiplier value="1.000000" />
        <WeaponImpulseMultiplier value="1.000000" />
        <RotationLimitAngle value="0.000000" />
        <TorqueAngularVelocityLimit value="5.000000" />
        <StdDoorRotDir>StdDoorOpenBothDir</StdDoorRotDir>
      </Tuning>
    </Item>
  </NamedTuningArray>
  <ModelToTuneMapping>
    <Item>
      <ModelName>bh1_48_gate_1</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_arm_gate_l</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_bh1_48_backdoor_l</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_bh1_48_backdoor_r</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_ch_025c_g_door_01</ModelName>
      <TuningName>FranklinGarage2</TuningName>
    </Item>
    <Item>
      <ModelName>prop_com_gar_door_01</ModelName>
      <TuningName>DefaultGarageCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_com_ls_door_01</ModelName>
      <TuningName>ChopShopGarageCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_cs4_05_tdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_cs4_10_tr_gd_01</ModelName>
      <TuningName>TrevorGarageCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_door_balcony_left</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_door_balcony_right</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_01</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_01b</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_03_l</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_03_r</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_04_l</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_04_r</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_05_r</ModelName>
      <TuningName>LargeGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_06_l</ModelName>
      <TuningName>StandardHeavy</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_06_r</ModelName>
      <TuningName>StandardHeavy</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_11</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_01gate1</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_02gate1</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_02gate5</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_02gate6_l</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_02gate6_r</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_02gate7</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_03gate4</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_03gate5</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_04gate1</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_06gate2</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_06gate3</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_07gate1</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_07gate1</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_07gate2</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_07gate3</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fnclink_09gate1</ModelName>
      <TuningName>LargeFenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fncres_02_gate1</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fncres_03gate1</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_fncwood_07gate1</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_gar_door_03_ld</ModelName>
      <TuningName>GangGarageCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_gate_cult_01_l</ModelName>
      <TuningName>CultistGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_gate_cult_01_r</ModelName>
      <TuningName>CultistGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_gate_tep_01_l</ModelName>
      <TuningName>LargeGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_gate_tep_01_r</ModelName>
      <TuningName>LargeGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_gate_farm_01a</ModelName>
      <TuningName>FenceGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_ld_bankdoors_01</ModelName>
      <TuningName>HeavyWoodDoor</TuningName>
    </Item>
    <Item>
      <ModelName>prop_ld_garaged_01</ModelName>
      <TuningName>MichaelGarageCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_lrggate_02_ld</ModelName>
      <TuningName>LargeGate</TuningName>
    </Item>
    <Item>
      <ModelName>prop_police_door_l</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_police_door_r</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>prop_sc1_21_g_door_01</ModelName>
      <TuningName>FranklinGarageCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_sec_barrier_ld_01a</ModelName>
      <TuningName>FragBarrierArmCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>prop_sec_barrier_ld_02a</ModelName>
      <TuningName>TollBarrierArmCustomBox</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_arm_secdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_bk_vaultdoor</ModelName>
      <TuningName>HeavyVaultDoor</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_bl_doorsl_l</ModelName>
      <TuningName>SlidingHorizontalExtendedRange</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_bl_doorsl_r</ModelName>
      <TuningName>SlidingHorizontalExtendedRange</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_cd_entrydoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_cor_firedoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_cor_firedoorwide</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_fa_dinedoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_fa_frontdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_fa_roomdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_fbisecgate</ModelName>
      <TuningName>FBISecurityGate</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_fib_door3</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_gc_door01</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_gc_door02</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_gc_door03</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_gc_door04</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_lostdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_mm_door</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_mm_doorm_l</ModelName>
      <TuningName>NoRotationLimitStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_mm_doorm_r</ModelName>
      <TuningName>NoRotationLimitStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_mm_doorw</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ph_gendoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ph_gendoor002</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ph_gendoor003</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_phroofdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_spraydoor</ModelName>
      <TuningName>SprayDoors</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ss_door02</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ss_door5_l</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ss_door5_r</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ss_door7</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_ss_door8</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_trev_door</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_trev_door</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_trev_doorbath</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_trev_doorfront</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_trevtrailerdr</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>vb_35_lifeguarddoor2</ModelName>
      <TuningName>ReduceOpenGarage</TuningName>
    </Item>
    <Item>
      <ModelName>prop_facgate_08</ModelName>
      <TuningName>TowTruckYardSlidingHorizontal</TuningName>
    </Item>
    <Item>
      <ModelName>hei_prop_station_gate</ModelName>
      <TuningName>SlidingHorizontalPoliceSPVehiclesAndMPPlayerPeds</TuningName>
    </Item>
    <Item>
      <ModelName>lr_prop_supermod_door_01</ModelName>
      <TuningName>SupermodGarage2</TuningName>
    </Item>
	  <Item>
      <ModelName>amb_mhc_hotel_floor_ent_door_l</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
	  <Item>
      <ModelName>amb_mhc_hotel_floor_int_door_l</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_bollards1</ModelName>
      <TuningName>BollardPolice</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_bollards2</ModelName>
      <TuningName>BollardPolice</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_cells_door</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_room13_parkingdoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_reception_entrancedoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_door_04</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_mrpd_door_03</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>tr_prop_tr_roller_door_09a_mc</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>v_corp_hicksdoor_pawnhub</ModelName>
      <TuningName>DefaultStandard</TuningName>
    </Item>
    <Item>
      <ModelName>sanhje_parkranger_celldoor</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>sanhje_parkranger_door</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>tr_prop_tr_roller_door_09a_vagos_ramp</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>tr_prop_tr_roller_door_09a_vagos</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>apa_prop_ss1_mpint_garage2_vpd</ModelName>
      <TuningName>VPDGarage</TuningName>
    </Item>
    <Item>
      <ModelName>denis3d_hayes_auto_shuttergate</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>lr_prop_warehouse_door_01</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>nutt_rockford_garage_door</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>mutiny_hookies_gate_door2</ModelName>
      <TuningName>SlidingHorizontalPoliceSPVehiclesAndMPPlayerPeds</TuningName>
    </Item>
    <Item>
      <ModelName>mutiny_hookies_gate_door1_r</ModelName>
      <TuningName>SlidingHorizontalPoliceSPVehiclesAndMPPlayerPeds</TuningName>
    </Item>
    <Item>
      <ModelName>mutiny_hookies_gate_door1_l</ModelName>
      <TuningName>SlidingHorizontalPoliceSPVehiclesAndMPPlayerPeds</TuningName>
    </Item>
    <Item>
      <ModelName>mutiny_hookies_int_door002</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>mutiny_hookies_int_door1</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
      <Item>
      <ModelName>xee_digital_den_picture_door</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>ajaxon_burton_lsc_side_door2</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>ajaxon_burton_lsc_front_door</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>gabz_firedept_garage_door</ModelName>
      <TuningName>FranklinGarage2</TuningName>
    </Item>
    <Item>
      <ModelName>hedwig_sheriff_garage_gardoor</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>custom_base_entrance_gate</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>v_ilev_bl_shutter2</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
    <Item>
      <ModelName>hedwig_pillbox_door01a</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</TuningName>
    </Item>
    <Item>
      <ModelName>hedwig_pillbox_door01b</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</TuningName>
    </Item>
    <Item>
      <ModelName>hedwig_pillbox_door03a</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</TuningName>
    </Item>
    <Item>
      <ModelName>hedwig_pillbox_door03b</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</TuningName>
    </Item>
        <Item>
      <ModelName>as_rex_garage_fdoor01</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>as_rex_garage_fdoor02</ModelName>
      <TuningName>DefaultStandardLatchShut</TuningName>
    </Item>
    <Item>
      <ModelName>as_reds_auto_gar_door</ModelName>
      <TuningName>as_customs_reds_auto_gar</TuningName>
    </Item>
	<Item>
      <ModelName>as_garagedoor</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>as_garagedoor2</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>western_pentdoor01</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>patoche_cyber_door3lod</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</TuningName>
    </Item>
	<Item>
      <ModelName>patoche_cyber_door2lod</ModelName>
      <TuningName>SlidingHorizontalSPVehiclesAndMPPlayerPeds_Pillbox</TuningName>
    </Item>
	<Item>
      <ModelName>nteam_mechanic_garagedoor1</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>hedwig_pillbox_garagedoor</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>gl_amc_g_garagedoor</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>gl_amc_g_seperator</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>apollo_puerta_gdoor</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>apollo_puerta_gar_win_02</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>apollo_puerta_gar_win_03</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
	<Item>
      <ModelName>apollo_puerta_gdoor02</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
  <Item>
      <ModelName>bi_garage_door_01</ModelName>
      <TuningName>FatGarage</TuningName>
    </Item>
  </ModelToTuneMapping>
</CDoorTuningFile>