local isDisabled = true
local currentWeapon = false
local oldCamera = nil

local function IsPlayerFirstPerson()
    return GetFollowVehicleCamViewMode() == 4
end

local function SetCameraMode(mode)
    for i = 1, 7 do
        SetCamViewModeForContext(i, mode)
    end
    SetFollowVehicleCamViewMode(mode)
end

local blockRearShootingVehicles = {}
local blockShootingFromVehicles = {}

RegisterNetEvent("blockRearShootingVehicles")
AddEventHandler("blockRearShootingVehicles", function(vehs)
    blockRearShootingVehicles = vehs
end)

RegisterNetEvent("blockShootingFromVehicles")
AddEventHandler("blockShootingFromVehicles", function(vehs)
    blockShootingFromVehicles = vehs
end)

local damageModifierActive = false

function lookingBehind()
    local coord = GetOffsetFromEntityInWorldCoords(PlayerPedId(), 0.0, -8.0, 0.0)
    local onScreen = World3dToScreen2d(coord.x, coord.y, coord.z)
    return onScreen
end

Citizen.CreateThread(function()
    while true do
        Wait(0)
        local ped = PlayerPedId()
        local veh = GetVehiclePedIsUsing(ped)

        if IsPedInAnyVehicle(ped, false) then
            damageModifierActive = true
            SetPlayerWeaponDamageModifier(PlayerId(), 0.5)

            local vehModel = tostring(GetEntityModel(veh))
            local vehClass = GetVehicleClass(veh)
            local isDriver = GetPedInVehicleSeat(veh, -1) == ped
            local isLookingBehind = lookingBehind()
            local currentWeapon = GetSelectedPedWeapon(ped)

            if vehClass == 8 or vehClass == 15 then
                SetPlayerCanDoDriveBy(PlayerId(), true)
            elseif blockShootingFromVehicles[vehModel] and isDriver then
                SetPlayerCanDoDriveBy(PlayerId(), false)
            elseif blockRearShootingVehicles[vehModel] == 1 and isDriver and isLookingBehind then
                SetPlayerCanDoDriveBy(PlayerId(), false)
            elseif blockRearShootingVehicles[vehModel] == 2 and isLookingBehind then
                SetPlayerCanDoDriveBy(PlayerId(), false)
            elseif currentWeapon == `WEAPON_STUNGUN` or currentWeapon == `WEAPON_STUNGUN2` then
                SetPlayerCanDoDriveBy(PlayerId(), false)    
            elseif isLookingBehind then
                SetPlayerCanDoDriveBy(PlayerId(), false)
            else
                SetPlayerCanDoDriveBy(PlayerId(), true)
            end
        elseif damageModifierActive then
            SetPlayerWeaponDamageModifier(PlayerId(), 1.0)
            damageModifierActive = false
        end
    end
end)
