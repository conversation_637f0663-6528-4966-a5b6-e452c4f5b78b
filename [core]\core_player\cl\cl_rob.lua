local function isTargetPlayerDead(targetServerId)
    local targetPlayer = GetPlayerFromServerId(targetServerId)
    if targetPlayer then
        return Player(targetPlayer).state.isDead
    end
    return false
end

local function getPlayerJob(targetServerId)
    local job = lib.callback.await('base_player:getJob', false, targetServerId)
    return job or "unknown"
end

local function getClosestPlayer(radius)
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local closestPlayer, closestServerId, closestDistance = nil, nil, radius

    for _, playerId in ipairs(GetActivePlayers()) do
        if playerId ~= PlayerId() then
            local targetPed = GetPlayerPed(playerId)
            local targetCoords = GetEntityCoords(targetPed)
            local distance = #(playerCoords - targetCoords)

            if distance < closestDistance then
                closestPlayer = targetPed
                closestServerId = GetPlayerServerId(playerId)
                closestDistance = distance
            end
        end
    end

    return closestPlayer, closestServerId
end

local function checkCooldown()
    local cooldown = lib.callback.await('base_player:checkCD', false)
    if cooldown > 0 then
        lib.notify({
            description = ('You must wait %d minutes before robbing again.'):format(math.ceil(cooldown / 60)),
            type = 'error',
            position = 'bottom-right'
        })
        return false
    end
    return true
end

local function startRobbery()
    if not checkCooldown() then return end
    local closestPlayer, targetServerId = getClosestPlayer(3.0)
    if not closestPlayer or not targetServerId then
        lib.notify({
            description = 'You must be near a player to rob them!',
            type = 'error',
            position = 'bottom-right'
        })
        return
    end

    local isTargetDead = isTargetPlayerDead(targetServerId)
    local targetJob = getPlayerJob(targetServerId)

    if isTargetDead and targetJob == "police" then
        lib.notify({
            description = 'You cannot rob a dead police officer!',
            type = 'error',
            position = 'bottom-right'
        })
        return
    end

    if lib.progressBar({
        duration = math.random(5000, 7000),
        label = 'Robbing Player...',
        position = 'bottom',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true,
        },
        anim = {
            dict = 'random@shop_robbery',
            clip = 'robbery_action_b'
        },
    }) then
        ExecuteCommand('me ~r~ Searching Player ~r~')
        exports.ox_inventory:openNearbyInventory()
        lib.callback.await('base_player:RobberyCD', false, isTargetDead)
    else
        lib.notify({
            description = 'You stopped robbing the player.',
            type = 'inform',
            position = 'bottom-right'
        })
    end
end

RegisterCommand('rob', function()
    startRobbery()
end, false)

RegisterCommand('k', function()
    ExecuteCommand('e surrender')
end, false)