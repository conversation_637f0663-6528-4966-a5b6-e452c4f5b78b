---------------------
--- Main Thread
---------------------
local crouched = false

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if IsControlJustPressed(0, 36) then -- Left Control key
            local ped = PlayerPedId()
            if not IsPedInAnyVehicle(ped, false) then
                crouched = not crouched
                if crouched then
                    RequestAnimSet("move_ped_crouched")
                    while not HasAnimSetLoaded("move_ped_crouched") do
                        Citizen.Wait(1)
                    end
                    SetPedMovementClipset(ped, "move_ped_crouched", 0.25)
                else
                    ResetPedMovementClipset(ped, 0.25)
                end
            end
        end
    end
end)
