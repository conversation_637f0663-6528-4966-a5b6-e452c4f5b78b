lib.locale()
local config = lib.require('config')
local locations = lib.require('locations')
local activeRobberies = {}
local activeCooldowns = {}


AddEventHandler('onResourceStart', function(resource)
    
    if resource ~= cache.resource then return end
end)

AddEventHandler('onResourceStop', function(resource)
    
    if resource ~= cache.resource then return end
end)


lib.callback.register('cypher_crime:exitCurrentRobbery', function(source, id)
    local currentRobbery = activeRobberies[id]
    local cooldown = os.time() + config.robCooldown * 60

    if not currentRobbery then return false end

    local coords = locations[id].coords

    SetPlayerRoutingBucket(source, 0)

    activeRobberies[id] = false 
    activeCooldowns[id] = cooldown

    return coords
end)


lib.callback.register('cypher_crime:canBreakIn', function(source, coords)
    local time = os.time()

    for i=1, #locations do 
        local location = locations[i]
        local dist = #(coords - location.coords)

        if dist < config.robDistance then 
            if activeRobberies[i] then 
                lib.notify(source,{
                    description = locale("notify.beingRobbed"), 
                    type = 'error'
                })

                return false
            end
            if activeCooldowns[i] then 
                if activeCooldowns[i] > time then 
                    lib.notify(source,{
                        description = locale("notify.robbedRecently"), 
                        type = 'error'
                    })
                    return false 
                end 
            end

            return {id = i, type = location.type, property = location.property, coords = location.coords}
        end
    end

    lib.notify(source,{
        description = locale("notify.noHousesNearby"), 
        type = 'error'
    })
    return false
end)

lib.callback.register('cypher_crime:prepareBreakIn', function(source, id)
    local time = os.time()
    math.randomseed(time)
    local routingBucket = math.random(50, 999)
    local location = locations[id]
    local property = config.properties[location.property][location.type]
    local stashes = {}
    
    for _, searchpoint in ipairs(property.searchPoints) do
        local selectedLoot = {}
    
        for _, loot in ipairs(searchpoint.loot) do
            local randomChance = math.random(1, 100)
            if randomChance <= loot.chance then
                table.insert(selectedLoot, {loot.item, loot.amount})
            end
        end

        local mystash = exports.ox_inventory:CreateTemporaryStash({
            label = 'Looting',
            slots = 25,
            maxWeight = 5000,
            items = selectedLoot
        })

        local stash = {
            inv = mystash, 
            coords = searchpoint.coords,
            minigame = searchpoint.minigame and searchpoint.minigame or false,
            customText = searchpoint.customText and searchpoint.customText or false,
            model = searchpoint.model and searchpoint.model or false,
            modelCoords = searchpoint.modelCoords and searchpoint.modelCoords or false
        }
        
        table.insert(stashes, stash)
    end

    local activeRobbery = {
        id = id, 
        type = location.type, 
        property = location.property,
        routingBucket = routingBucket
    }

    activeRobberies[id] = activeRobbery
    SetPlayerRoutingBucket(source, routingBucket)

    return stashes
end)