local function IsPlayerAtOceanFloor()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local waterHeight = GetWaterHeight(coords.x, coords.y, coords.z)

    return waterHeight and coords.z < -15
end

local xpThresholds = {
    0, 500, 750, 1125, 2531, 3769, 8250, 12500, 18800, 24000, 28500, 33400, 38700, 44200, 50200, 56400, 63000, 69900, 77100, 84700, 92500,
    100700, 109200, 118000, 127100, 136500, 146200, 156200, 166500, 177100, 188000, 199200, 210700, 222400, 234500, 246800, 259400, 272300,
    285500, 299000, 312700, 326800, 341000, 355600, 370500, 385600, 401000, 416600, 432600, 448800, 465200, 482000, 499000, 516300, 533800,
    551600, 569600, 588000, 606500, 625400, 644500, 663800, 683400, 703300, 723400, 743800, 764500, 785400, 806500, 827900, 849600, 871500,
    893600, 916000, 938700, 961600, 984700, 1008100, 1031800, 1055700, 1079800, 1104200, 1128800, 1153700, 1178800, 1204200, 1229800, 1255600,
    1281700, 1308100, 1334600, 1361400, 1388500, 1415800, 1443300, 1471100, 1499100, 1527300, 1555800, 1584350
}

Config = {
    SearchTimes = {
        {minLevel = 0, maxLevel = 10, minTime = 4000, maxTime = 5000},
        {minLevel = 11, maxLevel = 20, minTime = 3750, maxTime = 4500},
        {minLevel = 21, maxLevel = 30, minTime = 3350, maxTime = 4250},
        {minLevel = 31, maxLevel = 40, minTime = 2950, maxTime = 4000},
        {minLevel = 41, maxLevel = 50, minTime = 2500, maxTime = 3750},
        {minLevel = 51, maxLevel = 100, minTime = 2200, maxTime = 3500}
    }
}

local function getLevelFromXP(xp)
    for i = #xpThresholds, 1, -1 do
        if xp >= xpThresholds[i] then
            return i
        end
    end
    return 1
end

local function getSearchTime(level)
    for _, range in ipairs(Config.SearchTimes) do
        if level >= range.minLevel and level <= range.maxLevel then
            return math.random(range.minTime, range.maxTime)
        end
    end
    return math.random(3000, 5000)
end


CreateThread(function()
    local textDisplayed = false
    local lastSearchTime = 0
    local searchCooldown = 10000
    local lastOceanCheck = 0
    local isAtOceanFloor = false

    while true do
        Wait(250)

        local currentTime = GetGameTimer()
        if currentTime - lastOceanCheck >= 500 then
            lastOceanCheck = currentTime
            isAtOceanFloor = IsPlayerAtOceanFloor()
        end

        if isAtOceanFloor then
            if not textDisplayed then
                textDisplayed = true
                exports["base_drawtext"]:displayText("Diving", "Search the ocean floor", "G", nil, function()
                end)
            end

            if IsControlJustReleased(0, 47) then
                local ped = PlayerPedId()
                local currentTime = GetGameTimer()

                if currentTime - lastSearchTime < searchCooldown then
                    local timeLeft = math.ceil((searchCooldown - (currentTime - lastSearchTime)) / 1000)
                    lib.notify({
                        type = "inform",
                        icon = 'fa-solid fa-person-swimming',
                        description = "You need to wait " .. timeLeft .. " seconds before searching again.",
                    })
                    return
                end

                lastSearchTime = currentTime

                local currentWeapon = GetSelectedPedWeapon(ped)
                if currentWeapon ~= GetHashKey("WEAPON_KNIFE") then
                    lib.notify({
                        type = "error",
                        icon = 'fa-solid fa-person-swimming',
                        description = "You need a knife to search the ocean floor.",
                    })
                    return
                end

                local currentXP = exports["base_skills"]:CheckSkill("Diving") or 0
                local currentLevel = getLevelFromXP(currentXP)
                local searchTime = getSearchTime(currentLevel)

                FreezeEntityPosition(ped, true)

                Wait(searchTime)

                local skillCheck = lib.skillCheck({
                    {areaSize = 40, speedMultiplier = 1},
                    {areaSize = 30, speedMultiplier = 1.2},
                    {areaSize = 20, speedMultiplier = 1.5},
                })

                ClearPedTasks(ped)
                FreezeEntityPosition(ped, false)

                if skillCheck then
                    local baseXp = math.random(12, 21)
                    local xpGain = xpBoostActive and (baseXp * 2) or baseXp

                    exports["base_skills"]:UpdateSkill("Diving", xpGain)
                    TriggerServerEvent("main_job_scavange:scuba")
                else
                    lib.notify({
                        type = "error",
                        icon = 'fa-solid fa-person-swimming',
                        description = "You failed to collect anything from the ocean floor.",
                    })
                end
            end
        else
            if textDisplayed then
                exports["base_drawtext"]:hideText()
                textDisplayed = false
            end
        end
    end
end)


RegisterNetEvent('main_job_scavange:useXpBoost', function()
    if xpBoostActive then
        lib.notify({
            description = "You already have an active XP boost.",
            type = "inform",
            icon = "bolt"
        })
        return
    end

    local success = lib.progressBar({
        duration = 5000,
        label = "Using XP Boost...",
        useWhileDead = false,
        canCancel = true,
        disable = {
            move = true,
            combat = true
        },
        anim = {
            dict = "mp_common",
            clip = "givetake2_a",
            flag = 49
        }
    })

    if success then
        xpBoostActive = true
        xpBoostTimer = GetGameTimer() + (30 * 60 * 1000)

        lib.notify({
            description = "You now earn 2x XP for diving for the next 30 minutes!",
            type = "success",
            icon = "bolt"
        })

        CreateThread(function()
            while xpBoostActive do
                Wait(5000)
                if GetGameTimer() > xpBoostTimer then
                    xpBoostActive = false
                    lib.notify({
                        description = "Your XP boost has expired.",
                        type = "inform",
                        icon = "bolt"
                    })
                    break
                end
            end
        end)
    else
        lib.notify({
            description = "You canceled using the XP boost.",
            type = "error",
            icon = "bolt"
        })
    end
end)