<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Inventory Styling</title>
    <link rel="stylesheet" href="css/custom.css">
</head>
<body>
    <script>
        // Inject custom CSS into ox_inventory
        window.addEventListener('message', function(event) {
            if (event.data.action === 'setupInventory') {
                // Wait for ox_inventory to load, then inject our custom styles
                setTimeout(() => {
                    const inventoryFrame = parent.document.querySelector('iframe[src*="ox_inventory"]');
                    if (inventoryFrame && inventoryFrame.contentDocument) {
                        const customCSS = `
                            <link rel="stylesheet" href="nui://ox_inventory_custom/html/css/custom.css">
                        `;
                        inventoryFrame.contentDocument.head.insertAdjacentHTML('beforeend', customCSS);
                    }
                }, 1000);
            }
        });

        // Alternative method: Direct style injection
        function injectCustomStyles() {
            const style = document.createElement('style');
            style.textContent = \`
                /* Custom inventory styles will be loaded from CSS file */
            \`;
            document.head.appendChild(style);
        }

        // Try to inject styles when page loads
        document.addEventListener('DOMContentLoaded', injectCustomStyles);
    </script>
</body>
</html>
