RegisterServerEvent('fx_chat:init')
AddEventHandler('fx_chat:init', function()
    local source = source

end)

RegisterCommand('say', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    TriggerClientEvent('fx_chat:addMessage', -1, {
        type = 'message',
        title = GetPlayerName(source),
        message = message,
        color = '#ffffff'
    })
end, false)

RegisterCommand('me', function(source, args, rawCommand)
    local message = table.concat(args, ' ')
    TriggerClientEvent('fx_chat:addMessage', -1, {
        type = 'action',
        title = GetPlayerName(source),
        message = message,
        color = '#c2a2da'
    })
end, false)