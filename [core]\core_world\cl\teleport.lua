local TeleportLocations = require 'shared.config'

local function DrawText3D(coords, text)
    local x, y = World3dToScreen2d(coords.x, coords.y, coords.z)
    local factor = #text / 370
    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(true)
    AddTextComponentString(text)
    DrawText(x, y)
    DrawRect(x, y + 0.0125, 0.015 + factor, 0.03, 0, 0, 0, 100)
end

CreateThread(function()
    while true do
        local sleep = 1000
        local coords = GetEntityCoords(cache.ped)
        for id, loc in pairs(TeleportLocations) do
            local dist = #(coords - loc.marker)
            if dist < 20.0 then
                sleep = 0
                DrawMarker(1, loc.marker.x, loc.marker.y, loc.marker.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.2, 1.2, 1.2, 30, 144, 255, 120, false, true, 2, nil, nil, false)
                if dist < 2.0 then
                    DrawText3D(loc.marker + vec3(0, 0, 1.0), "[G] " .. id)
                    if IsControlPressed(0, 47) then
                        local done = exports['main_progressbar']:Progress({
                            name = "tp_"..id,
                            duration = 3000,
                            label = "Teleporting...",
                            useWhileDead = false,
                            canCancel = false,
                            controlDisables = {
                                disableMovement = true,
                                disableCarMovement = true,
                                disableCombat = true
                            }
                        })
                        if done then
                            DoScreenFadeOut(150)
                            Wait(150)
                            TriggerServerEvent('myteleport:teleport', id)
                            Wait(150)
                            DoScreenFadeIn(150)
                        end
                    end
                end
            end
        end
        Wait(sleep)
    end
end)
