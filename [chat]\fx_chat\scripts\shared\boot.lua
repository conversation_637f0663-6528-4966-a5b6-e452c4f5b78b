Chat = {
    _cache = {
        active = false,
        activating = false,
        hidden = true,
        loaded = false,
    }
}

function Chat.refresh()
    -- Refresh chat display
    if Chat._cache.loaded then
        SendNUIMessage({
            type = 'ON_REFRESH'
        })
    end
end

function Chat.message(data)
    SendNUIMessage({
        type = 'ON_MESSAGE',
        message = data
    })
end

function Chat.addSuggestion(name, help, params)
    SendNUIMessage({
        type = 'ON_SUGGESTION_ADD',
        suggestion = {
            name = name,
            help = help,
            params = params or {}
        }
    })
end

function Chat.removeSuggestion(name)
    SendNUIMessage({
        type = 'ON_SUGGESTION_REMOVE',
        name = name
    })
end

function Chat.addSuggestions(suggestions)
    for _, suggestion in ipairs(suggestions) do
        Chat.addSuggestion(suggestion.name, suggestion.help, suggestion.params)
    end
end

function Chat.push(message)
    SendNUIMessage({
        type = 'ON_MESSAGE',
        message = message
    })
end

function Chat.clear()
    SendNUIMessage({
        type = 'ON_CLEAR'
    })
end
