
local QBCore = exports['qb-core']:GetCoreObject()

local animDict = "missminuteman_1ig_2"
local anim = "handsup_base"
local handsup = false

RegisterKeyMapping('hu', 'Put your hands up', 'KEYBOARD', 'X')

RegisterCommand('hu', function()
    local ped = PlayerPedId()
    if IsPedInAnyVehicle(ped) then
        handsup = not handsup
    end

    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Citizen.Wait(100)
    end

    handsup = not handsup

    if QBCore.Functions.GetPlayerData().metadata["isdead"] or QBCore.Functions.GetPlayerData().metadata["inlaststand"] then
        return
    end

    if GetResourceState('wasabi_police') == 'started' and exports.wasabi_police:IsHandcuffed() then
        return
    end

    if handsup then
        CreateThread(function()
            while handsup do
                Wait(1)
                DisableControlAction(0, 24, true)
                DisableControlAction(0, 25, true)
                DisablePlayerFiring(ped, true)
            end
        end)
        TriggerEvent('ox_inventory:disarm', true)
        TaskPlayAnim(ped, animDict, anim, 8.0, 8.0, -1, 50, 0, false, false, false)
        if IsPedInAnyVehicle(ped, false) then
            local vehicle = GetVehiclePedIsIn(ped, false)
            if GetPedInVehicleSeat(vehicle, -1) == ped then
                Citizen.CreateThread(function()
                end)
            end
        end    
    else
        ClearPedTasks(ped)
    end
end, false)