<?xml version="1.0" encoding="UTF-8"?>
<!-- #♥FS-Graphics♥	 -->
<rage__ptxgpuDropRenderSettings>
  <useAlphaThreshold value="false" />
  <useMistMap value="false" />
  <useTopViewSwitch value="false" />
  <transitionFade value="false" />
  <textureRowsColsStartEnd x="1.000000" y="4.000000" z="8.000000" w="4.000000" />
  <textureAnimRateScaleOverLifeStart2End2 x="0.000000" y="0.000000" z="0.000000" w="3.000000" />
  <sizeMinMax x="0.012000" y="0.200000" z="0.002000" w="0.420000" />
  <startFrameMin_Max x="0.000000" y="0.000000" />
  <animLoopRate_MaxLoops x="0.000000" y="0.000000" />
  <rateBasedOnParticleLife value="false" />
  <directionalFlipUV x="0.000000" y="0.000000" />
  <colour x="0.140000" y="0.140000" z="0.140000" w="0.180000" />
  <alphaVariation value="0.000000" />
  <fadeInOut x="0.010000" y="0.010000" />
  <fadeNearFar x="0.100000" y="25.000000" />
  <fadeGrdOffLoHi x="0.000000" y="-5.000000" z="1.000000" w="100.000000" />
  <rotSpeedMinMax x="0.000000" y="0.000000" />
  <rotStartMinMax x="0.000000" y="0.000000" />
  <uvFlipChance x="0.000000" y="0.000000" />
  <directionalZOffsetMinMax x="1.000000" y="0.000000" z="0.000000" />
  <directionalZSizeRangeSpeedRange x="1.000000" y="200.000000" z="0.000000" w="1.000000" />
  <dirVelAddCamSpeedMinMaxMult x="10.000000" y="50.000000" z="0.500000" />
  <edgeSoftness value="0.900000" />
  <particleColorPercentage value="1.000000" />
  <backgroundDistortionVisibilityPercentage value="20.000000" />
  <backgroundDistortionAlphaBooster value="3.000000" />
  <backgroundDistortionAmount value="100.000000" />
  <backgroundDistortionBlurLevel value="2" />
  <directionalAndAmbientMultiplier value="2.000000" />
  <localLightsMultiplier value="40.000000" />
  <directionalLightShadowAmount value="1.000000" />
  <translucency value="0.800000" />
  <wrapLightingTerm value="1.000000" />
  <lightFalloffBiasModifier value="2.000000" />
  <dofAlphaOutputMultiplier value="36.099998" />
  <dofOutputMode>DOF_OUTPUT_COLOR_WITH_ALPHA</dofOutputMode>
  <timeModifiers>
    <enabled value="true" />
    <sizeMultiplier x="3.000000" y="0.050000" z="3.000000" w="0.150000" />
    <distortionMultiplier value="2.000000" />
    <bloomMultiplier value="0.000000" />
  </timeModifiers>
</rage__ptxgpuDropRenderSettings>