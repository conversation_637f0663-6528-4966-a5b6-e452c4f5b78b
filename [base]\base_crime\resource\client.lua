lib.locale()
local config = lib.require('config')
local breakingIn = false
local robberyPeds = {}
local robberyModels = {}

local function spawnPeds(location)
    if not location.peds then return end
    math.randomseed(GetGameTimer())

    local owner = location.peds.owner
    local dog = location.peds.dog

    if owner then 
        if location.peds.ownerSpawnChance then 
            local randomChance = math.random(1, 100)
             
            if randomChance <= location.peds.ownerSpawnChance then 
                return             
            end
        end
        
        lib.requestModel(owner) -- Load Owner Ped

        robberyPeds['owner'] = CreatePed(28, owner, location.peds.ownerCoords.x,location.peds.ownerCoords.y,location.peds.ownerCoords.z, 0.0, true, true)
        SetModelAsNoLongerNeeded(owner)
    end 

    if dog then    
        if location.peds.dogSpawnChance then 
            local randomChance = math.random(1, 100)
             
            if randomChance <= location.peds.dogSpawnChance then 
                return             
            end
        end

        lib.requestModel(dog) 

        robberyPeds['dog'] = CreatePed(28, dog, location.peds.dogCoords.x, location.peds.dogCoords.y, location.peds.dogCoords.z, 0.0, true, true)

        SetEntityCoords(robberyPeds['dog'], location.peds.dogCoords.x, location.peds.dogCoords.y, location.peds.dogCoords.z, 0.0, 0.0, 0.0, false)
        SetEntityHeading(robberyPeds['dog'], location.peds.dogCoords.w)

        TaskCombatPed(robberyPeds['dog'], cache.ped, 0, 16)
    end 

    return true
end

local function spawnModels(stashes)
    for i=1, #stashes do 
        local stash = stashes[i]

        if stash.model then 
            lib.requestModel(stash.model)
            local model = robberyModels[#robberyModels+1]

            model = CreateObject(stash.model, stash.modelCoords.x, stash.modelCoords.y, stash.modelCoords.z, false, true)
            SetEntityHeading(model, stash.modelCoords.w)
            FreezeEntityPosition(model, true)
        end
    end
end

local function drawText(text, coords)
        
    local onScreen, _x, _y = World3dToScreen2d(coords.x, coords.y, coords.z+1)
    
    if onScreen then
        SetTextScale(0.4, 0.4)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 255)
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(true)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

local function exitRobbery(id)
    DoScreenFadeOut(1000)
    local currentRobbery = lib.callback.await('cypher_crime:exitCurrentRobbery', false, id)

    if not currentRobbery then return end 

    breakingIn = false
    for _, ped in pairs(robberyPeds) do 
        if DoesEntityExist(ped) then 
            DeleteEntity(ped)
        end 
    end

    for _, model in pairs(robberyModels) do 
        if DoesEntityExist(model) then 
            DeleteEntity(model)
        end 
    end

    while IsScreenFadingOut() do 
        Wait(50)
    end

    SetBlackout(false)

    SetEntityCoords(cache.ped, currentRobbery.x, currentRobbery.y, currentRobbery.z)

    DoScreenFadeIn(1000)

    lib.notify({
        description = locale("notify.leftRobbery"),
        type = 'inform'
    })    
end

local function drawNoiseText(x, y, width, height, scale, text, r, g, b, a, font)
    SetTextFont(font)
    SetTextProportional(0)
    SetTextScale(scale, scale)
    SetTextColour(r, g, b, a)
    SetTextDropShadow(0, 0, 0, 0, 255)
    SetTextEdge(2, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(x - width / 2, y - height / 2 + 0.005)
end

local function breakIn()
    local coords = GetEntityCoords(cache.ped)
    local canBreakIn = lib.callback.await('cypher_crime:canBreakIn', false, coords)

    if not canBreakIn then return end 

    local skillCheck = config.skillCheck('breakIn')

    if not skillCheck then return lib.notify({
        description = locale("notify.failedSkillCheck"),
        type = 'error',
        duration = 5000
    })
    end

    if not lib.progressBar({
        duration = config.robDuration * 1000,
        label = locale("progress.breakingIn"),
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true
        },
        anim = {
            dict = 'missheistfbi3b_ig7',
            clip = 'lift_fibagent_loop'
        }
    }) then 
        lib.notify({
            description = locale("notify.cancelledBreakIn"),
            type = 'error',
            duration = 5000
        })        
        return 
    end
    
    DoScreenFadeOut(1000)
    while IsScreenFadingOut() do 
        Wait(50)
    end

    local stashes = lib.callback.await('cypher_crime:prepareBreakIn', false, canBreakIn.id)


    local locationConfig = config.properties[canBreakIn.property][canBreakIn.type]

    SetEntityCoords(cache.ped, locationConfig.coords.x,locationConfig.coords.y, locationConfig.coords.z)
    SetEntityHeading(cache.ped, locationConfig.coords.w)

    spawnPeds(locationConfig)
    spawnModels(stashes)
    TriggerEvent('cypher_crime:robberyThread', canBreakIn, stashes)
    SetBlackout(true)
    return true
end

lib.callback.register('cypher_crime:swapAnimations', function()
    lib.progressBar({
        duration = config.craftDuration * 1000,
        label = locale("progress.crafting"),
        useWhileDead = false,
        canCancel = false,
        disable = {
            car = true,
            move = true
        },
        anim = {
            dict = 'mini@repair',
            clip = 'fixing_a_ped'
        }
    }) 
    return 
end)

exports('breakIn', breakIn)

AddEventHandler('cypher_crime:robberyThread', function(robbery, stashes)
    local locationConfig = config.properties[robbery.property][robbery.type]
    local noiseTimer = 0
    local caught = false
    local caughtAlready = false
   
    breakingIn = true 
    DoScreenFadeIn(1000)

    while breakingIn do 
        local coords = GetEntityCoords(cache.ped)

        if config.isPlayerDead(cache.ped) then
            exitRobbery(robbery.id)
        end

        local dist = #(coords - locationConfig.coords.xyz)
        if dist < 3.0 then 
            drawText(locale("text.exit"), locationConfig.coords)

        end

        if dist < 1.5 and IsControlJustPressed(0, 47) then
            exitRobbery(robbery.id)
        end

        if stashes then 
            for i=1, #stashes do 
                local stash = stashes[i]
                local dist = #(coords - stash.coords)
                if dist < 3.0 then 
                    local text = not stash.customText and locale("text.search") or stash.customText
                    drawText(text, stash.coords)
                end
                if dist < 2.0 and IsControlJustPressed(0, 47) then
                    if stash.minigame then 
                        local skillCheck = config.skillCheck(stash.minigame)

                        if skillCheck then 
                            lib.progressBar({
                                duration = 1000,
                                label = locale("progress.searching"),
                                useWhileDead = false,
                                canCancel = false,
                                anim = {
                                    dict = 'mini@triathlon', 
                                    clip = 'rummage_bag'
                                },
                                disable = {
                                    car = true,
                                    move = true
                                },
                            })
        
                            exports.ox_inventory:openInventory('stash', stash.inv)
                        end
                    else 
                        lib.progressBar({
                            duration = 1000,
                            label = locale("progress.searching"),
                            useWhileDead = false,
                            canCancel = false,
                            anim = {
                                dict = 'mini@triathlon', 
                                clip = 'rummage_bag'
                            },
                            disable = {
                                car = true,
                                move = true
                            },
                        })
    
                        exports.ox_inventory:openInventory('stash', stash.inv)
                    end
                end
            end
        end

        if not caughtAlready then  
            if (GetEntitySpeed(cache.ped) < 1.4) then
                noiseTimer = GetGameTimer()
            end
    
            local loudTime = (GetGameTimer() - noiseTimer)
            if (loudTime > 2000) then
                caught = true
            end
    
            if IsPedJumping(cache.ped) then
                caught = true
            end     
            local percentage = math.min(loudTime / 2500, 1.0) 
            local otherColour = math.floor(255 - (255 * percentage))
            drawNoiseText(config.hud.x,config.hud.y,config.hud.width, config.hud.height, config.hud.scale, locale("text.noise"), 255, otherColour, otherColour, 255, 4)
        end

    

        if caught or IsPedShooting(cache.ped) then
            if not caughtAlready then

                SetBlackout(false)
                lib.notify({
                    title = locale("notify.caught"),
                    type = 'error'
                })                
                --ClearPedTasksImmediately(robbery.ped)
                TaskJump(robberyPeds['owner'])

                SetRelationshipBetweenGroups(5, GetPedRelationshipGroupHash(cache.ped), GetPedRelationshipGroupHash(robberyPeds['owner']))
                SetRelationshipBetweenGroups(5, GetPedRelationshipGroupHash(robberyPeds['owner']), GetPedRelationshipGroupHash(cache.ped))

                local pedWeapon = config.weapons[math.random(#config.weapons)]
                GiveWeaponToPed(robberyPeds['owner'], pedWeapon, 250, false, true)
                TaskCombatPed(robberyPeds['owner'], cache.ped, 0, 16)
                

                caughtAlready = true

                config.policeDispatch(cache.ped, robbery.coords)
            end
        end

        Wait(100)
    end

end)

