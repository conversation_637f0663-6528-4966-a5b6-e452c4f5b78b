Citizen.CreateThread(function()
    -- ====================================================================
    -- =--------------------- [GTA V: Single player] ---------------------=
    -- ====================================================================

    -- Michael: -802.311, 175.056, 72.8446
    Michael.LoadDefault()

    -- Simeon: -47.16170 -1115.3327 26.5
    Simeon.LoadDefault()

    -- <PERSON>'s aunt: -9.96562, -1438.54, 31.1015
    FranklinAunt.LoadDefault()

    -- <PERSON>.<PERSON>efault()

    -- Floyd: -1150.703, -1520.713, 10.633
    Floyd.LoadDefault()

    -- Trevor: 1985.48132, 3828.76757, 32.5
    <PERSON><PERSON><PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>ault()

    -- Ba<PERSON><PERSON> Mamas: -1388.0013, -618.41967, 30.819599
    BahamaMamas.Enable(true)

    -- Pillbox hospital: 307.1680, -590.807, 43.280
    PillboxHospital.Enable(true)

    -- Zancudo Gates (GTAO like): -1600.30100000, 2806.73100000, 18.79683000
    ZancudoGates.LoadDefault()

    -- Other
    Ammunations.LoadDefault()
    LesterFactory.LoadDefault()
    StripClub.LoadDefault()
    CargoShip.LoadDefault()

    Graffitis.Enable(true)

    -- UFO
    UFO.Hippie.Enable(false) -- 2490.47729, 3774.84351, 2414.035
    UFO.Chiliad.Enable(false) -- 501.52880000, 5593.86500000, 796.23250000
    UFO.Zancudo.Enable(false) -- -2051.99463, 3237.05835, 1456.97021

    -- Red Carpet: 300.5927, 199.7589, 104.3776
    RedCarpet.Enable(false)

    -- North Yankton: 3217.697, -4834.826, 111.8152
    NorthYankton.Enable(false)

    -- ====================================================================
    -- =-------------------------- [GTA Online] --------------------------=
    -- ====================================================================
    GTAOApartmentHi1.LoadDefault() -- -35.31277 -580.4199 88.71221 (4 Integrity Way, Apt 30)
    GTAOApartmentHi2.LoadDefault() -- -1477.14 -538.7499 55.5264 (Dell Perro Heights, Apt 7)
    GTAOHouseHi1.LoadDefault() -- -169.286 486.4938 137.4436 (3655 Wild Oats Drive)
    GTAOHouseHi2.LoadDefault() -- 340.9412 437.1798 149.3925 (2044 North Conker Avenue)
    GTAOHouseHi3.LoadDefault() -- 373.023 416.105 145.7006 (2045 North Conker Avenue)
    GTAOHouseHi4.LoadDefault() -- -676.127 588.612 145.1698 (2862 Hillcrest Avenue)
    GTAOHouseHi5.LoadDefault() -- -763.107 615.906 144.1401 (2868 Hillcrest Avenue)
    GTAOHouseHi6.LoadDefault() -- -857.798 682.563 152.6529 (2874 Hillcrest Avenue)
    GTAOHouseHi7.LoadDefault() -- 120.5 549.952 184.097 (2677 Whispymound Drive)
    GTAOHouseHi8.LoadDefault() -- -1288 440.748 97.69459 (2133 Mad Wayne Thunder)
    GTAOHouseMid1.LoadDefault() -- 347.2686 -999.2955 -99.19622
    GTAOHouseLow1.LoadDefault() -- 261.4586 -998.8196 -99.00863

    -- ====================================================================
    -- =------------------------ [DLC: High life] ------------------------=
    -- ====================================================================
    HLApartment1.LoadDefault() -- -1468.14 -541.815 73.4442 (Dell Perro Heights, Apt 4)
    HLApartment2.LoadDefault() -- -915.811 -379.432 113.6748 (Richard Majestic, Apt 2)
    HLApartment3.LoadDefault() -- -614.86 40.6783 97.60007 (Tinsel Towers, Apt 42)
    HLApartment4.LoadDefault() -- -773.407 341.766 211.397 (EclipseTowers, Apt 3)
    HLApartment5.LoadDefault() -- -18.07856 -583.6725 79.46569 (4 Integrity Way, Apt 28)
    HLApartment6.LoadDefault() -- -609.56690000 51.28212000 -183.98080

    -- ====================================================================
    -- =-------------------------- [DLC: Heists] -------------------------=
    -- ====================================================================
    HeistCarrier.Enable(true) -- 3082.3117, -4717.1191, 15.2622
    HeistYacht.LoadDefault() -- -2043.974,-1031.582, 11.981

    -- ====================================================================
    -- =--------------- [DLC: Executives & Other Criminals] --------------=
    -- ====================================================================
    ExecApartment1.LoadDefault() -- -787.7805 334.9232 215.8384 (EclipseTowers, Penthouse Suite 1)
    ExecApartment2.LoadDefault() -- -773.2258 322.8252 194.8862 (EclipseTowers, Penthouse Suite 2)
    ExecApartment3.LoadDefault() -- -787.7805 334.9232 186.1134 (EclipseTowers, Penthouse Suite 3)

    -- ====================================================================
    -- =-------------------- [DLC: Finance  & Felony] --------------------=
    -- ====================================================================
    FinanceOffice1.LoadDefault() -- -141.1987, -620.913, 168.8205 (Arcadius Business Centre)
    FinanceOffice2.LoadDefault() -- -75.8466, -826.9893, 243.3859 (Maze Bank Building)
    FinanceOffice3.LoadDefault() -- -1579.756, -565.0661, 108.523 (Lom Bank)
    FinanceOffice4.LoadDefault() -- -1392.667, -480.4736, 72.04217 (Maze Bank West)

    -- ====================================================================
    -- =-------------------------- [DLC: Bikers] -------------------------=
    -- ====================================================================
    BikerCocaine.LoadDefault() -- Cocaine lockup: 1093.6, -3196.6, -38.99841
    BikerCounterfeit.LoadDefault() -- Counterfeit cash factory: 1121.897, -3195.338, -40.4025
    BikerDocumentForgery.LoadDefault() -- Document forgery: 1165, -3196.6, -39.01306
    BikerMethLab.LoadDefault() -- Meth lab: 1009.5, -3196.6, -38.99682
    BikerWeedFarm.LoadDefault() -- Weed farm: 1051.491, -3196.536, -39.14842
    BikerClubhouse1.LoadDefault() -- 1107.04, -3157.399, -37.51859
    BikerClubhouse2.LoadDefault() -- 998.4809, -3164.711, -38.90733

    -- ====================================================================
    -- =---------------------- [DLC: Import/Export] ----------------------=
    -- ====================================================================
    ImportCEOGarage1.LoadDefault() -- Arcadius Business Centre
    ImportCEOGarage2.LoadDefault() -- Maze Bank Building /!\ Do not load parts Garage1, Garage2 and Garage3 at the same time (overlaping issues)
    ImportCEOGarage3.LoadDefault() -- Lom Bank /!\ Do not load parts Garage1, Garage2 and Garage3 at the same time (overlaping issues)
    ImportCEOGarage4.LoadDefault() -- Maze Bank West /!\ Do not load parts Garage1, Garage2 and Garage3 at the same time (overlaping issues)
    ImportVehicleWarehouse.LoadDefault() -- Vehicle warehouse: 994.5925, -3002.594, -39.64699

    -- ====================================================================
    -- =------------------------ [DLC: Gunrunning] -----------------------=
    -- ====================================================================
    GunrunningBunker.LoadDefault() -- 892.6384, -3245.8664, -98.2645
    GunrunningYacht.LoadDefault() -- -1363.724, 6734.108, 2.44598

    -- ====================================================================
    -- =---------------------- [DLC: Smuggler's Run] ---------------------=
    -- ====================================================================
    SmugglerHangar.LoadDefault() -- -1267.0 -3013.135 -49.5

    -- ====================================================================
    -- =-------------------- [DLC: The Doomsday Heist] -------------------=
    -- ====================================================================
    DoomsdayFacility.LoadDefault()

    -- ====================================================================
    -- =----------------------- [DLC: After Hours] -----------------------=
    -- ====================================================================
    AfterHoursNightclubs.LoadDefault() -- -1604.664, -3012.583, -78.000

    -- ====================================================================
    -- =------------------- [DLC: Diamond Casino Resort] -----------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2060 then
        DiamondCasino.LoadDefault() -- 1100.000, 220.000, -50.000
        DiamondPenthouse.LoadDefault() -- 976.636, 70.295, 115.164
    end

    -- ====================================================================
    -- =------------------- [DLC: Los Santos Tuners] ---------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2372 then
        TunerGarage.LoadDefault() -- -1350.0, 160.0, -100.0
        TunerMethLab.LoadDefault() -- 981.9999, -143.0, -50.0
        TunerMeetup.LoadDefault() -- -2000.0, 1113.211, -25.36243
    end

    -- ====================================================================
    -- =------------------- [DLC: Los Santos The Contract] ---------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2545 then
        MpSecurityGarage.LoadDefault() -- -1071.4387, -77.033875, -93.525505
        MpSecurityMusicRoofTop.LoadDefault() -- -592.6896, 273.1052, 116.302444
        MpSecurityStudio.LoadDefault() -- -1000.7252, -70.559875, -98.10669
        MpSecurityBillboards.LoadDefault() -- -592.6896, 273.1052, 116.302444
        MpSecurityOffice1.LoadDefault() -- -1021.86084, -427.74564, 68.95764
        MpSecurityOffice2.LoadDefault() -- 383.4156, -59.878227, 108.4595
        MpSecurityOffice3.LoadDefault() -- -1004.23035, -761.2084, 66.99069
        MpSecurityOffice4.LoadDefault() -- -587.87213, -716.84937, 118.10156
    end

    -- ====================================================================
    -- =------------------- [DLC: The Criminal Enterprise] ---------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2699 then
        CriminalEnterpriseSmeonFix.LoadDefault() -- -50.2248, -1098.8325, 26.049742
        CriminalEnterpriseVehicleWarehouse.LoadDefault() -- 800.13696, -3001.4297, -65.14074
        CriminalEnterpriseWarehouse.LoadDefault() -- 849.1047, -3000.209, -45.974354
    end

    -- ====================================================================
    -- =------------------- [DLC: Los Santos Drug Wars] ------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2802 then
        DrugWarsFreakshop.LoadDefault() -- 570.9713, -420.0727, -70.000
        DrugWarsGarage.LoadDefault() -- 519.2477, -2618.788, -50.000
        DrugWarsLab.LoadDefault() -- 483.4252, -2625.071, -50.000
    end

    -- ====================================================================
    -- =------------------- [DLC: San Andreas Mercenaries] ---------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2944 then
        MercenariesClub.LoadDefault() -- 1202.407, -3251.251, -50.000
        MercenariesLab.LoadDefault() -- -1916.119, 3749.719, -100.000
        MercenariesFixes.LoadDefault()
    end

    -- ====================================================================
    -- =------------------- [DLC: The Chop Shop] -------------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 3095 then
        ChopShopCargoShip.LoadDefault() -- -344.4349, -4062.832, 17.000
        ChopShopCartelGarage.LoadDefault() -- 1220.133, -2277.844, -50.000
        ChopShopLifeguard.LoadDefault() -- -1488.153, -1021.166, 5.000
        ChopShopSalvage.LoadDefault() -- 1077.276, -2274.876, -50.000
    end
end)
