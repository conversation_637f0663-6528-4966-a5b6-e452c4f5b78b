local seatbeltOn = false
local harnessOn = false
local harnessHp = 20
local handbrake = 0
local sleep = 0
local harnessData = {}
local newvehicleBodyHealth = 0
local currentvehicleBodyHealth = 0
local frameBodyChange = 0
local frameEngineChange = 0
local lastFrameVehiclespeed = 0
local lastFrameVehiclespeed2 = 0
local thisFrameVehicleSpeed = 0
local tick = 0
local damagedone = false
local modifierDensity = true
local effectActive = false
local currAccidentLevel = 0
local disableControls = false

    --anti vdm
Citizen.CreateThread(function()
    while true do
        N_0x4757f00bc6323cfe(-**********, 0.0) 
        Wait(0)
    end
    end)

    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(4)
            local ped = PlayerPedId()
        end
    end)


RegisterNetEvent("crashEffectMinimalzzz")
AddEventHandler("crashEffectMinimalzzz", function(countDown, accidentLevel)
    local ped = PlayerPedId()
if not effectActive or (accidentLevel > currAccidentLevel) then
    currAccidentLevel = accidentLevel
    disableControls = true
    effectActive = true
    blackOutActive = true
    blackOutActive = false
    SetTimecycleModifier("hud_def_blur")
    ShakeGameplayCam("SMALL_EXPLOSION_SHAKE", (accidentLevel * Config.ScreenShakeMultiplier))      
        countDown = countDown - 1
        if countDown < Config.TimeLeftToEnableControls and disableControls then
            disableControls = false
        end
    currAccidentLevel = 0
    effectActive = false
    ClearTimecycleModifier()
    Wait(60000)
    ResetPedMovementClipset(ped)
end
end)


RegisterNetEvent("crashEffectMinimal")
AddEventHandler("crashEffectMinimal", function(countDown, accidentLevel)
    local ped = PlayerPedId()
if not effectActive or (accidentLevel > currAccidentLevel) then
    currAccidentLevel = accidentLevel
    disableControls = true
    effectActive = true
    blackOutActive = true
    blackOutActive = false
    StartScreenEffect('Dont_tazeme_bro', 0, true)
    while countDown > 0 do
        if countDown > (1.2*accidentLevel)   then 
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", (accidentLevel * Config.ScreenShakeMultiplier))
        end 
        Wait(50)        
        countDown = countDown - 1
        if countDown < Config.TimeLeftToEnableControls and disableControls then
            disableControls = false
        end
        if countDown <= 1 then
            StopScreenEffect('Dont_tazeme_bro')
        end
    end
    currAccidentLevel = 0
    effectActive = false
    Wait(60000)
    ResetPedMovementClipset(ped)
end
end)


RegisterNetEvent("crashEffect")
AddEventHandler("crashEffect", function(countDown, accidentLevel)
    local ped = PlayerPedId()
if not effectActive or (accidentLevel > currAccidentLevel) then
    currAccidentLevel = accidentLevel
    disableControls = true
    effectActive = true
    blackOutActive = true
    blackOutActive = false


    while countDown > 0 do
        if countDown > (3.5*accidentLevel)   then 
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", (accidentLevel * Config.ScreenShakeMultiplier))
            
        end 
        Wait(750)        
        countDown = countDown - 1

        if countDown < Config.TimeLeftToEnableControls and disableControls then
            disableControls = false
        end
        if countDown <= 1 then
        end
    end
    currAccidentLevel = 0
    effectActive = false
    Wait(60000)
    ResetPedMovementClipset(ped)
end
end)




RegisterNetEvent("crashEject1")
AddEventHandler("crashEject1", function(countDown, accidentLevel)
    local ped = PlayerPedId()
if not effectActive or (accidentLevel > currAccidentLevel) then
    currAccidentLevel = accidentLevel
    disableControls = true
    effectActive = true
    blackOutActive = true
    blackOutActive = false
    StartScreenEffect('MP_race_crash', 0, true)
    while countDown > 0 do
        if countDown > (3.5*accidentLevel)   then 
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", (accidentLevel * Config.ScreenShakeMultiplier))
        end 
        Wait(300)        
        countDown = countDown - 1

        if countDown < Config.TimeLeftToEnableControls and disableControls then
            disableControls = false
        end
        if countDown <= 1 then
            StopScreenEffect('MP_race_crash')
        end
    end
    currAccidentLevel = 0
    effectActive = false
end
end)

RegisterNetEvent("crashEject2")
AddEventHandler("crashEject2", function(countDown, accidentLevel)
    local ped = PlayerPedId()
if not effectActive or (accidentLevel > currAccidentLevel) then
    currAccidentLevel = accidentLevel
    disableControls = true
    effectActive = true
    blackOutActive = true
    blackOutActive = false
    StartScreenEffect('Dont_tazeme_bro', 0, true)
    StartScreenEffect('MP_race_crash', 0, true)
    while countDown > 0 do
        if countDown > (3.5*accidentLevel)   then 
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", (accidentLevel * Config.ScreenShakeMultiplier))
        end 
        Wait(300)        
        countDown = countDown - 1

        if countDown < Config.TimeLeftToEnableControls and disableControls then
            disableControls = false
        end
        if countDown <= 1 then
            StopScreenEffect('Dont_tazeme_bro')
            StopScreenEffect('MP_race_crash')
        end
    end
    currAccidentLevel = 0
    effectActive = false
end
end)

RegisterNetEvent("crashEffectFAST")
AddEventHandler("crashEffectFAST", function(countDown, accidentLevel)
    local ped = PlayerPedId()
if not effectActive or (accidentLevel > currAccidentLevel) then
    currAccidentLevel = accidentLevel
    disableControls = true
    effectActive = true
    blackOutActive = true
    blackOutActive = false
    StartScreenEffect('Dont_tazeme_bro', 0, true)
    StartScreenEffect('MP_race_crash', 0, true)
    while countDown > 0 do
        if countDown > (3.5*accidentLevel)   then 
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", (accidentLevel * Config.ScreenShakeMultiplier))
        end 
        Wait(30000)        
        countDown = countDown - 1

        if countDown < Config.TimeLeftToEnableControls and disableControls then
            disableControls = false
        end
        if countDown <= 1 then
            StopScreenEffect('Dont_tazeme_bro')
           StopScreenEffect('MP_race_crash')
        end
    end
    currAccidentLevel = 0
    effectActive = false
end
end)

RegisterCommand('toggleseatbelt', function()
    if IsPedInAnyVehicle(PlayerPedId(), false) then
        local class = GetVehicleClass(GetVehiclePedIsUsing(PlayerPedId()))
        if class ~= 8 and class ~= 13 and class ~= 14 then
            ToggleSeatbelt()
        end
    end
end, false)

RegisterKeyMapping('toggleseatbelt', 'Toggle Seatbelt', 'keyboard', 'B')

function ToggleSeatbelt()
    if seatbeltOn then
        seatbeltOn = false
        TriggerEvent("seatbelt:client:ToggleSeatbelt")
        TriggerServerEvent("InteractSound_SV:PlayOnSource", "carunbuckle", 0.25)
    else
        seatbeltOn = true
        TriggerEvent("seatbelt:client:ToggleSeatbelt")
        TriggerServerEvent("InteractSound_SV:PlayOnSource", "carbuckle", 0.25)
    end
end

function ToggleHarness()
    if harnessOn then
        harnessOn = false
    else
        harnessOn = true
        ToggleSeatbelt()
    end
end

function ResetHandBrake()
    if handbrake > 0 then
        handbrake = handbrake - 1
    end
end

function HasHarness()
    return harnessOn
end

CreateThread(function()
    while true do
        sleep = 1000
        if IsPedInAnyVehicle(PlayerPedId()) then
            sleep = 10
            if seatbeltOn or harnessOn then
                DisableControlAction(0, 75, true)
                DisableControlAction(27, 75, true)
            end
        else
            seatbeltOn = false
            harnessOn = false
        end
        Wait(sleep)
    end
end)

local IsDamageWall = true

AddEventHandler('gameEventTriggered', function(event, data)
    if event == "CEventNetworkEntityDamage" then
        local victim, attacker, victimDied, weapon = data[1], data[2], data[4], data[7]
        local playerPed = PlayerPedId()
        local playerVehicle = GetVehiclePedIsIn(playerPed, false)
        if IsEntityAVehicle(victim) and victim == playerVehicle then
            if IsPedInAnyVehicle(playerPed) and IsPedInAnyVehicle(attacker) and GetPedInVehicleSeat(playerVehicle, -1) == playerPed then
                if weapon == 133987706 then
                    IsDamageWall = true
                    Wait(250)
                    IsDamageWall = false
                end
            end
        end
    end
end)

CreateThread(function()
    while true do
        Wait(5)
        local playerPed = PlayerPedId()
        local currentVehicle = GetVehiclePedIsIn(playerPed, false)
        local driverPed = GetPedInVehicleSeat(currentVehicle, -1)
        if currentVehicle ~= nil and currentVehicle ~= false and currentVehicle ~= 0 then
            SetPedHelmet(playerPed, false)
            lastVehicle = GetVehiclePedIsIn(playerPed, false)
            if GetVehicleEngineHealth(currentVehicle) < 0.0 then
                SetVehicleEngineHealth(currentVehicle, 0.0)
            end
            if (GetVehicleHandbrake(currentVehicle) or (GetVehicleSteeringAngle(currentVehicle)) > 25.0 or (GetVehicleSteeringAngle(currentVehicle)) < -25.0) then
                if handbrake == 0 then
                    handbrake = 100
                    ResetHandBrake()
                else
                    handbrake = 100
                end
            end
            thisFrameVehicleSpeed = GetEntitySpeed(currentVehicle) * 3.6
            currentvehicleBodyHealth = GetVehicleBodyHealth(currentVehicle)
            if currentvehicleBodyHealth == 1000 and frameBodyChange ~= 0 then
                frameBodyChange = 0
            end
            if frameBodyChange ~= 0 then
                if lastFrameVehiclespeed > 90 and lastFrameVehiclespeed < 170 then
                    if IsDamageWall == true then  
                    TriggerEvent("crashEffectMinimalzzz", Config.EffectTimeLevel1, 1)
                    end
                end
                if lastFrameVehiclespeed > 170  then
                    if IsDamageWall == true then  
                    TriggerEvent("crashEffectMinimal", Config.EffectTimeLevel1, 1)
                    end
                end
                if lastFrameVehiclespeed > 40 and thisFrameVehicleSpeed < (lastFrameVehiclespeed * 0.75) and not damagedone then

                    if frameBodyChange > 1 then
                        if not seatbeltOn and not IsThisModelABike(currentVehicle) then
                            if math.ceil(lastFrameVehiclespeed) > 199 then
                                if not harnessOn then
                                    EjectFromVehicleFAST()

                                else
                                    harnessHp = harnessHp - 1
                                    TriggerServerEvent('seatbelt:DoHarnessDamage', harnessHp, harnessData)
                                end
                            end
                        elseif (seatbeltOn or harnessOn) and not IsThisModelABike(currentVehicle) then
                            if lastFrameVehiclespeed > 300 then
                                if math.random(math.ceil(lastFrameVehiclespeed)) > 10 then
                                    if not harnessOn then
                                        EjectFromVehicleFASTwithSeatbelt()
                                    else
                                        harnessHp = harnessHp - 1

                                    end                   
                    else
                        if not seatbeltOn and not IsThisModelABike(currentVehicle) then
                            if math.ceil(lastFrameVehiclespeed) > 40 and math.ceil(lastFrameVehiclespeed) < 150 then
                                if not harnessOn then
                                    if IsDamageWall == true then  
                                    TriggerEvent("crashEffect", Config.EffectTimeLevel2, 5)
                                    end
                                else
                                    harnessHp = harnessHp - 1
                                    TriggerServerEvent('seatbelt:DoHarnessDamage', harnessHp, harnessData)
                                end    
                                if math.ceil(lastFrameVehiclespeed) > 150 and math.ceil(lastFrameVehiclespeed) < 199 then
                            EjectFromVehicleSLOW()
                                end

                            end
                        elseif (seatbeltOn or harnessOn) and not IsThisModelABike(currentVehicle) then
                            if lastFrameVehiclespeed > 300 then
                                    if not harnessOn then
                                   EjectFromVehicleFASTwithSeatbelt()
                                     end                     
                                end
                            end
                        end
                    end
                end
            end
                    damagedone = true
                end
                if currentvehicleBodyHealth < 350.0 and not damagedone then
                    damagedone = true
                    Wait(1000)
                end
            end
            if lastFrameVehiclespeed < 100 then
                Wait(100)
                tick = 0
            end
            frameBodyChange = newvehicleBodyHealth - currentvehicleBodyHealth
            if tick > 0 then 
                tick = tick - 1
                if tick == 1 then
                    lastFrameVehiclespeed = GetEntitySpeed(currentVehicle) * 3.6
                end
            else
                if damagedone then
                    damagedone = false
                    frameBodyChange = 0
                    lastFrameVehiclespeed = GetEntitySpeed(currentVehicle) * 3.6
                end
                lastFrameVehiclespeed2 = GetEntitySpeed(currentVehicle) * 3.6
                if lastFrameVehiclespeed2 > lastFrameVehiclespeed then
                    lastFrameVehiclespeed = GetEntitySpeed(currentVehicle) * 3.6
                end
                if lastFrameVehiclespeed2 < lastFrameVehiclespeed then
                    tick = 25
                end

            end
            vels = GetEntityVelocity(currentVehicle)
            if tick < 0 then 
                tick = 0
            end     
            newvehicleBodyHealth = GetVehicleBodyHealth(currentVehicle)
            if not modifierDensity then
                modifierDensity = true
            end
            veloc = GetEntityVelocity(currentVehicle)
        else
            if lastVehicle ~= nil then
                SetPedHelmet(playerPed, true)
                Wait(200)
                newvehicleBodyHealth = GetVehicleBodyHealth(lastVehicle)
                if not damagedone and newvehicleBodyHealth < currentvehicleBodyHealth then
                    damagedone = true
                    Wait(1000)
                end
                lastVehicle = nil
            end
            lastFrameVehiclespeed2 = 0
            lastFrameVehiclespeed = 0
            newvehicleBodyHealth = 0
            currentvehicleBodyHealth = 0
            frameBodyChange = 0
            Wait(2000)
        end
    end
end)

function GetFwd(entity)
    local hr = GetEntityHeading(entity) + 90.0
    if hr < 0.0 then hr = 360.0 + hr end
    hr = hr * 0.0174533
    return { x = math.cos(hr) * 5.73, y = math.sin(hr) * 5.73 }
end

function EjectFromVehicle()
    TriggerEvent("crashEject1", Config.EffectTimeLevel3, 5)
    local ped = PlayerPedId()
    Wait(1)
    SetPedToRagdoll(ped, 5511, 5511, 0, 0, 0, 0)
    TriggerEvent("Injurytimer:seatbelt")

end

function EjectFromVehicleSLOW()
    TriggerEvent("crashEffect", Config.EffectTimeLevel3, 5)
    local ped = PlayerPedId()
    Wait(1)
    TriggerEvent("Injurytimer:seatbelt")
end

function EjectFromVehicleFAST()
    TriggerEvent("crashEject1", Config.EffectTimeLevel3, 5)
    local ped = PlayerPedId()
    local veh = GetVehiclePedIsIn(ped,false)
    Wait(1)
    SetPedToRagdoll(ped, 5511, 5511, 0, 0, 0, 0)
    TriggerEvent("Injurytimer:seatbelt")

end

function EjectFromVehicleFASTwithSeatbelt()
    TriggerEvent("crashEject2", Config.EffectTimeLevel3, 5)
    local ped = PlayerPedId()
    Wait(1)
    SetPedToRagdoll(ped, 5511, 5511, 0, 0, 0, 0)
    TriggerEvent("Injurytimer:seatbelt")

end

RegisterNetEvent("Injurytimer:seatbelt")
AddEventHandler("Injurytimer:seatbelt", function(countDown)
    Wait(60000)
end)

function LightScreenPump()
    TriggerEvent("crashEffect", Config.EffectTimeLevel3, 5)
end