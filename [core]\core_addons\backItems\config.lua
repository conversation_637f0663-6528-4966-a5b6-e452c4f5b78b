---@class Slot
---@field bone number
---@field pos vector3
---@field rot vector3

---@class OptionalVector
---@field x? number
---@field y? number
---@field z? number

---@class BackItem
---@field prio number
---@field group? string
---@field customPos? {bone?: number , pos?: OptionalVector | vector3,  rot?:  OptionalVector | vector3}
---@field ignoreLimits? boolean
---@field model? number | string

---@class Config
---@field defaultSlots table<string, Slot[]>
---@field BackItems table<string, BackItem>

local Config = {}

Config.defaultSlots = {
    ["front"] = {
        { bone = 24818, pos = vec3(-0.02, 0.21, -0.02), rot = vec3(0.0, 330.0, 180.0) },
    },
    ["back_2"] = {
        { bone = 24818, pos = vec3(-0.05, -0.15, -0.02), rot = vec3(0.0, 165.0, 0.0) },
    },
    ["pickaxe_slot"] = {
        { bone = 24818, pos = vec3(-0.30, -0.16, 0.22), rot = vec3(0.0, 130.0, 0.0) },
    },
    ["sickle_slot"] = {
        { bone = 24818, pos = vec3(-0.0038, -0.16, -0.05), rot = vec3(0, 50.8, 0) },
    },
    ["melee"] = {
        { bone = 24818, pos = vec3(0.29, -0.16, 0.12), rot = vec3(0.0, -90.0, 0.0) },
        { bone = 24818, pos = vec3(0.29, -0.16, 0.00), rot = vec3(0.0, -90.0, 0.0) },
        { bone = 24818, pos = vec3(0.29, -0.16, -0.12), rot = vec3(0.0, -90.0, 0.0) },
    },
    ["scythe"] = {
        { bone = 24818, pos = vec3(-0.05, -0.16, 0.00), rot = vec3(0.0, 120.0, 0.0) },
    },
    ["right"] = {
        { bone = 51826, pos = vec3(-0.32, 0.04, -0.23), rot = vec3(90.0, 160.0, 10.0) },
        { bone = 51826, pos = vec3(-0.39, -0.03, -0.23), rot = vec3(90.0, 160.0, 10.0) },
    },
    ["tec9"] = {
        { bone = 58271, pos = vec3(-0.32, 0.04, 0.19), rot = vec3(270.0, 180.0, 40.0) },
    },
}

Config.BackItems = {
    ["WEAPON_MP7_CHROMIUM"] = { prio = 5, group = "tec9" },
    ["WEAPON_SHOTGUN_CHROMIUM"] = { prio = 5, group = "back_2" },
    ["WEAPON_SUNDA"] = { prio = 5, group = "front" },
    ["WEAPON_HOWAT20"] = { prio = 5, group = "front" },
    ["WEAPON_LMTM4R"] = { prio = 5, group = "back_2" },
    ["WEAPON_GROZA"] = { prio = 5, group = "front" },
    ["WEAPON_L85"] = { prio = 5, group = "back_2" },
    ["WEAPON_M4_HALLOWEEN"] = { prio = 5, group = "back_2" },
    ["WEAPON_SCEVO"] = { prio = 5, group = "back_2" },
    ["WEAPON_VIOLET_VENGANGE_CHR"] = { prio = 5, group = "back_2" },
    ["WEAPON_RPG"] = { prio = 5, group = "back_2" },
    ["WEAPON_FIREWORK"] = { prio = 5, group = "back_2" },
    ["WEAPON_MG"] = { prio = 5, group = "back_2" },
    ["WEAPON_COMBATMG"] = { prio = 5, group = "back_2" },
    ["WEAPON_SPECIALCARBINE"] = { prio = 5, group = "front" },
    ["WEAPON_SPECIALCARBINE_POL"] = { prio = 5, group = "front" },
    ["WEAPON_C36"] = { prio = 5, group = "front" },
    ["WEAPON_R9"] = { prio = 5, group = "front" },
    ["WEAPON_M4A1BLOCK2"] = { prio = 5, group = "front" },
    ["WEAPON_M27"] = { prio = 5, group = "back_2" },
    ["WEAPON_MK47BANSHEE"] = { prio = 5, group = "back_2" },
    ["WEAPON_SPECIALCARBINE_MK2"] = { prio = 5, group = "front" },
    ["WEAPON_GK47"] = { prio = 5, group = "back_2" },
    ["WEAPON_HK416"] = { prio = 4, group = "front" },
    ["WEAPON_XM7_6_8"] = { prio = 4, group = "front" },
    ["WEAPON_MK47FM"] = { prio = 3, group = "front" },
    ["WEAPON_HEAVYSNIPER"] = { prio = 4, group = "back_2" },
    ["WEAPON_COMBATPDW"] = { prio = 3, group = "back_2" },
    ["WEAPON_PUMPSHOTGUN"] = { prio = 2, group = "back_2" },
    ["WEAPON_REMINGTON"] = { prio = 2, group = "back_2" },
    ["WEAPON_PUMPSHOTGUN_MK2"] = { prio = 3, group = "back_2" },
    ["WEAPON_ADVANCEDRIFLE"] = { prio = 3, group = "back_2" },
    ["WEAPON_BULLPUPRIFLE"] = { prio = 3, group = "front" },
    ["WEAPON_CARBINERIFLE"] = { prio = 3, group = "back_2" },
    ["WEAPON_CARBINERIFLE_MK2"] = { prio = 3, group = "back_2" },
    ["WEAPON_COMPACTRIFLE"] = { prio = 3, group = "tec9" },
    ["WEAPON_AK47"] = { prio = 3, group = "back_2" },
    ["WEAPON_P90"] = { prio = 3, group = "back_2" },
    ["WEAPON_AK103"] = { prio = 3, group = "back_2" },
    ["WEAPON_WARSAW"] = { prio = 3, group = "back_2" },
    ["WEAPON_M4A5V2"] = { prio = 3, group = "back_2" },
    ["WEAPON_ASSAULTRIFLE_MK2"] = { prio = 3, group = "back_2" },
    ["WEAPON_SCARH"] = { prio = 3, group = "front" },
    ["WEAPON_M4ASIIMOV"] = { prio = 100, group = "front" },
    ["WEAPON_SAR"] = { prio = 3, group = "back_2" },
    ["WEAPON_FAMAS_YELLOW"] = { prio = 3, group = "back_2" },
    ["WEAPON_L85_CHRISTMAS"] = { prio = 3, group = "back_2" },
    ["WEAPON_FOOLV2_RED"] = { prio = 3, group = "back_2" },
    ["WEAPON_NVRIFLE_PURPLE"] = { prio = 3, group = "back_2" },
    ["WEAPON_GALILARV2"] = { prio = 3, group = "back_2" },
    ["WEAPON_R90"] = { prio = 3, group = "back_2" },
    ["WEAPON_GLACIER"] = { prio = 3, group = "back_2" },
    ["WEAPON_MODULAR_RIFLE"] = { prio = 3, group = "back_2" },
    ["WEAPON_HEAVYSMG"] = { prio = 3, group = "back_2" },
    ["WEAPON_GVANDAL"] = { prio = 3, group = "back_2" },
    ["WEAPON_GRAUV2"] = { prio = 3, group = "back_2" },
    ["WEAPON_FAMAS"] = { prio = 3, group = "front" },
    ["WEAPON_SCAR-L"] = { prio = 3, group = "front" },
    ["WEAPON_BULLPUPRIFLE_MK2"] = { prio = 3, group = "back_2" },
    ["WEAPON_GAU_5A"] = { prio = 3, group = "back_2" },
    ["WEAPON_HEAVYRIFLE"] = { prio = 3, group = "front" },
    ["WEAPON_MILITARYRIFLE"] = { prio = 3, group = "front" },
    ["WEAPON_TACTICALRIFLE"] = { prio = 3, group = "back_2" },
    ["WEAPON_ASSAULTSMG"] = { prio = 3, group = "back_2" },
    ["WEAPON_GUSENBERG"] = { prio = 3, group = "back_2" },
    ["WEAPON_MICROSMG"] = { prio = 3, group = "tec9" },
    ["WEAPON_MINISMG"] = { prio = 3, group = "tec9" },
    ["WEAPON_SMG"] = { prio = 3, group = "back_2" },
    ["WEAPON_SMG_MK2"] = { prio = 3, group = "tec9" },
    ["WEAPON_ASSAULTSHOTGUN"] = { prio = 3, group = "back_2" },
    ["WEAPON_HEAVYSHOTGUN"] = { prio = 3, group = "back" },
}

return Config
