RegisterNetEvent('base_player:client:RepairWeapon', function()
    local success = lib.progressBar({
        duration = 10000,
        label = 'Repairing weapon...',
        useWhileDead = false,
        canCancel = false,
        disable = {
            move = true,
            car = true,
            combat = true,
            mouse = false
        },
        anim = {
            dict = 'mini@repair',
            clip = 'fixing_a_ped',
            flags = 49
        }
    })

    if success then
        TriggerServerEvent('base_player:server:RepairWeapon')
    else
        TriggerEvent('ox_lib:notify', {
            type = 'error',
            description = 'Repair cancelled!',
            duration = 3000
        })
    end
end)