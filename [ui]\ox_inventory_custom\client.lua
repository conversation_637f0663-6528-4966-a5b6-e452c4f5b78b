-- Custom ox_inventory styling client script
-- This script injects custom CSS to match the NoFX design

local customCSS = [[
/* Custom ox_inventory styling to match NoFX design */
.inventory-container, .inventory-wrapper, .inventory-grid, .inventory-main {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(10px) !important;
}

.inventory-header, .inventory-title, .header {
    background: linear-gradient(90deg, #2d2d2d 0%, #3a3a3a 100%) !important;
    color: #4a9b9b !important;
    border-bottom: 1px solid #4a9b9b !important;
    padding: 12px 16px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}

.inventory-weight, .weight-display, .capacity {
    background: rgba(74, 155, 155, 0.1) !important;
    color: #4a9b9b !important;
    border: 1px solid #4a9b9b !important;
    border-radius: 4px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.inventory-slot, .slot, .item-slot {
    background: rgba(45, 45, 45, 0.8) !important;
    border: 1px solid #404040 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    width: 64px !important;
    height: 64px !important;
    margin: 2px !important;
}

.inventory-slot:hover, .slot:hover, .item-slot:hover {
    border-color: #4a9b9b !important;
    background: rgba(74, 155, 155, 0.1) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 0 12px rgba(74, 155, 155, 0.3) !important;
}

.inventory-slot img, .slot img, .item-slot img, .item-image {
    width: 48px !important;
    height: 48px !important;
    object-fit: contain !important;
    filter: brightness(1.1) contrast(1.1) !important;
    border-radius: 4px !important;
}

.item-count, .item-quantity, .quantity {
    position: absolute !important;
    bottom: 2px !important;
    right: 2px !important;
    background: rgba(74, 155, 155, 0.9) !important;
    color: #ffffff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 1px 4px !important;
    border-radius: 3px !important;
    min-width: 16px !important;
    text-align: center !important;
}

.inventory-grid {
    display: grid !important;
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 4px !important;
    padding: 16px !important;
    max-width: 600px !important;
    max-height: 500px !important;
}

.inventory-left, .player-inventory {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
    margin-right: 16px !important;
}

.inventory-right, .external-inventory, .secondary-inventory {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
    margin-left: 16px !important;
}

.inventory-wrapper {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    display: flex !important;
    gap: 20px !important;
    z-index: 1000 !important;
    animation: inventoryFadeIn 0.3s ease-out !important;
}

@keyframes inventoryFadeIn {
    from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
    to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

.ox_inventory .inventory, .ox_inventory .container, .ox_inventory .main {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 2px solid #4a9b9b !important;
    border-radius: 8px !important;
}
]]

-- Function to inject CSS into NUI
local function InjectCustomCSS()
    SendNUIMessage({
        action = 'injectCSS',
        css = customCSS
    })
end

-- Hook into ox_inventory events
AddEventHandler('ox_inventory:openInventory', function()
    Wait(100) -- Small delay to ensure inventory is loaded
    InjectCustomCSS()
end)

-- Alternative method using exports if available
CreateThread(function()
    Wait(5000) -- Wait for resources to load
    
    -- Try to inject CSS when inventory is opened
    while true do
        Wait(1000)
        
        -- Check if inventory is open and inject CSS
        if exports.ox_inventory and exports.ox_inventory:isInventoryOpen then
            local isOpen = exports.ox_inventory:isInventoryOpen()
            if isOpen then
                InjectCustomCSS()
            end
        end
    end
end)

-- Register NUI callback for CSS injection
RegisterNUICallback('requestCustomCSS', function(data, cb)
    cb(customCSS)
end)
