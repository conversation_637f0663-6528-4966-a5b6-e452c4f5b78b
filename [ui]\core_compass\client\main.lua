local display = false

CreateThread(function()
    while true do
        Wait(0)
        
        if LocalPlayer.state.isLoggedIn then
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            
            local street1, street2 = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
            local streetName = GetStreetNameFromHashKey(street1)
            if street2 ~= 0 then
                streetName = streetName .. " " .. GetStreetNameFromHashKey(street2)
            end
            
            local postalCode = exports.postals:GetPostal(coords) or "000"
            local heading = math.floor(GetEntityHeading(playerPed))
            
            if not display then
                SendNUIMessage({
                    action = "opencompass"
                })
                display = true
            end
            
            SendNUIMessage({
                action = "updatecompass",
                heading = heading
            })
            
            SendNUIMessage({
                action = "updatestreet",
                street = streetName,
                zone = postalCode
            })
        else
            if display then
                SendNUIMessage({
                    action = "closecompass"
                })
                display = false
            end
        end
    end
end)
