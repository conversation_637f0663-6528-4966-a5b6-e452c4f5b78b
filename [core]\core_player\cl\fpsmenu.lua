local type = "reset"

RegisterCommand("fps",function()
    lib.registerContext({
        id = 'fpsmenu',
        title = 'FPS Menu',
        options = {
          {
            title = 'Reset',
            onSelect = function ()
                type = "reset"
                FPSBoosterUM(true,true,true,true,5.0,5.0,5.0,10.0,10.0,true,false)
                SetTimecycleModifier()
                ClearTimecycleModifier()
                ClearExtraTimecycleModifier()
            end
          },
          {
            title = 'Ultra Low',
            onSelect = function ()
                type = "ulow"
                FPSBoosterUM(false,false,true,false,0.0,0.0,0.0,0.0,0.0,false,nil)
                SetTimecycleModifier('yell_tunnel_nodirect')
            end
          },
          {
            title = 'Low',
            onSelect = function ()
                type = "low"
                FPSBoosterUM(false,false,true,false,0.0,0.0,0.0,5.0,5.0,false,nil)
                SetTimecycleModifier('yell_tunnel_nodirect')
            end
          },
          {
            title = 'Medium',
            onSelect = function ()
                type = "medium"
                FPSBoosterUM(true,false,true,false,5.0,3.0,3.0,3.0,3.0,false,false)
                SetTimecycleModifier('tunnel')
            end
          }
        }
      })
     
      lib.showContext('fpsmenu')

end)
CreateThread(function()
    while true do
        if type == "ulow" or type == "low" then
            ClearAllBrokenGlass()
            ClearAllHelpMessages()
            LeaderboardsReadClearAll()
            ClearBrief()
            ClearGpsFlags()
            ClearPrints()
            ClearSmallPrints()
            ClearReplayStats()
            LeaderboardsClearCacheData()
            ClearFocus()
            ClearHdArea()
            ClearPedBloodDamage(PlayerPedId())
            ClearPedWetness(PlayerPedId())
            ClearPedEnvDirt(PlayerPedId())
            ResetPedVisibleDamage(PlayerPedId())
            --ClearExtraTimecycleModifier()
           -- ClearTimecycleModifier()
            ClearOverrideWeather()
            ClearHdArea()
            DisableVehicleDistantlights(false)
            DisableScreenblurFade()
            SetRainLevel(0.0)
            SetWindSpeed(0.0)
            Wait(300)
        elseif type == "medium" then
            ClearAllBrokenGlass()
            ClearAllHelpMessages()
            LeaderboardsReadClearAll()
            ClearBrief()
            ClearGpsFlags()
            ClearPrints()
            ClearSmallPrints()
            ClearReplayStats()
            LeaderboardsClearCacheData()
            ClearFocus()
            ClearHdArea()
            SetWindSpeed(0.0)
            Wait(1000)
        else
            Wait(1500)
        end
       -- print(type)
    end
end)

function FPSBoosterUM(shadow,air,entity,dynamic,tracker,depth,bounds,distance,tweak,sirens,lights)
    RopeDrawShadowEnabled(shadow)
    CascadeShadowsClearShadowSampleType()
    CascadeShadowsSetAircraftMode(air)
    CascadeShadowsEnableEntityTracker(entity)
    CascadeShadowsSetDynamicDepthMode(dynamic)
    CascadeShadowsSetEntityTrackerScale(tracker)
    CascadeShadowsSetDynamicDepthValue(depth)
    CascadeShadowsSetCascadeBoundsScale(bounds)
    SetFlashLightFadeDistance(distance)
    SetLightsCutoffDistanceTweak(tweak)
    SetArtificialLightsState(lights)
end