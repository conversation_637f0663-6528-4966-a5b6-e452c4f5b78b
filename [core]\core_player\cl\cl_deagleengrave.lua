lib.callback.register("base_player:useEngraver", function()
    lib.callback("base_player:checkWeapon")
end)

lib.callback.register("base_player:openEngravingMenu", function()
    lib.registerContext({
        id = 'deagle_engraving_menu',
        title = 'Deagle Engraving',
        options = {
            {
                title = 'Gun Girl Engraving',
                description = 'Engrave your Desert Eagle into a Gun Girl Deagle',
                icon = 'gun',
                onSelect = function()
                    lib.callback("base_player:engraveWeapon", false, function(success)
                        if success then
                            lib.notify({ 
                                description = 'Your Desert Eagle is now a Gun Girl Deagle!', 
                                type = 'success' 
                            })
                        else
                            lib.notify({ 
                                description = 'You do not have the required weapon!', 
                                type = 'error' 
                            })
                        end
                    end, "WEAPON_GUNGIRL")
                end
            },
            {
                title = 'Printstream Engraving',
                description = 'Engrave your Desert Eagle into a Printstream Deagle',
                icon = 'gun',
                onSelect = function()
                    lib.callback("base_player:engraveWeapon", false, function(success)
                        if success then
                            lib.notify({ 
                                description = 'Your Desert Eagle is now a Printstream Deagle!', 
                                type = 'success' 
                            })
                        else
                            lib.notify({ 
                                description = 'You do not have the required weapon!', 
                                type = 'error' 
                            })
                        end
                    end, "WEAPON_PRINTSTREAMPISTOL50")
                end
            }
        }
    })

    lib.showContext('deagle_engraving_menu')
end)

lib.callback.register("base_player:engraveSuccess", function()
    lib.notify({ 
        description = 'Your Desert Eagle has been engraved!', 
        type = 'success' 
    })
end)

lib.callback.register("base_player:engraveFailed", function()
    lib.notify({ 
        description = 'Something went wrong!', 
        type = 'error' 
    })
end)

lib.callback.register("base_player:engraveFailed2", function()
    lib.notify({ 
        description = 'You do not have a Deagle!', 
        type = 'error' 
    })
end)

