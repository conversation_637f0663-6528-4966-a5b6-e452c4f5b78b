Citizen.CreateThread(function()
  while true do
      Citizen.Wait(500)
      local playerId = PlayerId()
      local playerPed = GetPlayerPed(-1)
      SetPlayerHealthRechargeMultiplier(playerId, 0.0)
      local playerHealth = GetEntityHealth(playerPed)
      SetEntityHealth(playerPed, playerHealth)
  end
end)


local config = {
  pedFrequency = 0.3, -- Controls the amount of random peds walking/standing around (0.0 = None | 0.5 = Half as Many | 1.0 = Normal | 2.0 = Twice as Many)
  trafficFrequency = 0.15, -- Controls the amount of vehicles driving around and also controls the amount of parked vehicles (0.0 = None | 0.5 = Half as Many| 1.0 = Normal | 2.0 = Twice as Many)
}

Citizen.CreateThread(function()
  while true do
      Citizen.Wait(0)
      SetPedDensityMultiplierThisFrame(config.pedFrequency)
      SetScenarioPedDensityMultiplierThisFrame(config.pedFrequency, config.pedFrequency)
  
      SetRandomVehicleDensityMultiplierThisFrame(config.trafficFrequency)
      SetVehicleDensityMultiplierThisFrame(config.trafficFrequency)
  end 
end)

-- CreateThread(function()
--   while true do
--       local playerPed = PlayerPedId()
--       local isInVehicle = IsPedInAnyVehicle(playerPed, false)
--       local vehicle = GetVehiclePedIsIn(playerPed, false)

--       if isInVehicle and vehicle then
--           local vehicleClass = GetVehicleClass(vehicle)
--           local vehicleModel = GetEntityModel(vehicle)
--           if vehicleModel == GetHashKey("fxxkevo") then
--               -- Allow shooting for the specific vehicle
--               EnableControlAction(0, 24, true) -- Left mouse button
--               EnableControlAction(0, 69, true) -- Attack with vehicle
--               EnableControlAction(0, 70, true) -- Attack vehicle
--               EnableControlAction(0, 92, true) -- Secondary attack vehicle
--               EnableControlAction(0, 114, true) -- Attack helicopter
--               EnableControlAction(0, 140, true) -- Melee attack
--               EnableControlAction(0, 141, true) -- Melee attack (Alt)
--               EnableControlAction(0, 142, true) -- Melee attack (Extra)
--           else
--               if vehicleClass ~= 15 and vehicleClass ~= 16 then
--                   DisableControlAction(0, 24, true)
--                   DisableControlAction(0, 69, true)
--                   DisableControlAction(0, 70, true)
--                   DisableControlAction(0, 92, true)
--                   DisableControlAction(0, 114, true)
--                   DisableControlAction(0, 140, true)
--                   DisableControlAction(0, 141, true)
--                   DisableControlAction(0, 142, true)
--               end
--           end
--       end
--       Wait(0)
--   end
-- end)

