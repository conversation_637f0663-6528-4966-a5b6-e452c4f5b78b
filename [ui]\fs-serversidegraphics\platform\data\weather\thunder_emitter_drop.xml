<?xml version="1.0" encoding="UTF-8"?>
<!-- #♥FS-Graphics♥	 -->
<rage__ptxgpuDropEmitterSettings>
  <boxCentreOffset x="0.000000" y="7.000000" z="4.000000" />
  <boxSize x="15.900000" y="15.000000" z="20.000000" />
  <lifeMinMax x="3.000000" y="6.000000" />
  <velocityMin x="0.000000" y="0.000000" z="-1.000000" />
  <velocityMax x="0.000000" y="0.000000" z="-2.000000" />
  <clampToGround value="false" />
  <applyBounce value="false" />
  <bounceDist value="0.000000" />
  <bounceRestVel value="0.000000" />
  <bounciness value="0.000000" />
  <bounceRandom value="1.000000" />
  <massMinMax x="1.000000" y="1.000000" />
  <massExponentModifier value="2.000000" />
  <windGlobalVariationMult value="1.000000" />
  <windPositionalVariationMult value="1.000000" />
  <windGustMult value="1.000000" />
  <windDisturbanceMult value="1.000000" />
  <noiseTexScale value="0.000000" />
  <noiseAmplitudeHorizontal value="0.000000" />
  <noiseAmplitudeVertical value="0.000000" />
  <mistMapSpawnRejectionAmt value="0.250000" />
  <sheetRadiusMinMax x="0.000000" y="0.000000" />
  <sheetIntervalMinMax x="0.000000" y="0.000000" />
  <sheetBurstMinMax x="0.000000" y="0.000000" />
  <timeModifiers>
    <enabled value="true" />
    <timeScaleMultiplier value="0.665000" />
  </timeModifiers>
</rage__ptxgpuDropEmitterSettings>