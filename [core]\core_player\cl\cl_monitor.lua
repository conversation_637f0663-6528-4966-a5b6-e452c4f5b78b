--Renames labels of certain graphic settings to align with current rules
Citizen.CreateThread(function()
    AddTextEntry("0xB2C51344", "Aspect Ratio - ~r~MUST BE YOUR MONITORS NATIVE RESOLUTION")
    AddTextEntry("0x74F73ED3", "Shadow Quality - ~r~MUST BE ATLEAST NORMAL")
  end)
  
  -- Objectives handler
  RegisterNetEvent("base_player:setObjectiveText")
  AddEventHandler("base_player:setObjectiveText", function(text, clearall, timeout)
      if clearall then
          objectiveText = false
      else
          objectiveText = true
          Citizen.CreateThread(function ()
              while true do
                  while objectiveText do
                      Citizen.Wait(1)
  
                      SetTextFont(4)
                      SetTextProportional(1)
                      SetTextScale(0.55, 0.55)
                      SetTextColour(255, 255, 255, 255)
                      SetTextDropShadow(0, 0, 0, 0, 255)
                      SetTextEdge(1, 0, 0, 0, 255)
                      SetTextDropShadow()
                      SetTextOutline()
                      SetTextWrap(0.0, 1.0)
                      SetTextCentre(true)
                      SetTextJustification(0)
                      SetTextEntry('STRING')
                      AddTextComponentString(text)
                      DrawText(0.5, 0.89)
                  end
  
                  break
              end
          end)
      end
  
      Wait(timeout)
      objectiveText = false
  end)