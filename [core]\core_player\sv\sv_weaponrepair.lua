exports.qbx_core:CreateUseableItem('gunrepair', function(source, item)
    TriggerClientEvent('base_player:client:RepairWeapon', source)
end)

RegisterNetEvent('base_player:server:RepairWeapon', function()
    local source = source
    local weapon = exports.ox_inventory:GetCurrentWeapon(source)

    if weapon then
        local removed = exports.ox_inventory:RemoveItem(source, 'gunrepair', 1)

        if removed then
            exports.ox_inventory:SetDurability(source, weapon.slot, 100)

            TriggerClientEvent('ox_lib:notify', source, {
                type = 'success',
                description = 'You have successfully repaired your weapon!',
                duration = 3000
            })
        else
            exports.qbx_core:ExploitBan(source, 'C2 - Cheating | LUA Environment Tampered')
        end
    else
        TriggerClientEvent('ox_lib:notify', source, {
            type = 'error',
            description = 'You are not holding a weapon!',
            duration = 3000
        })
    end
end)
