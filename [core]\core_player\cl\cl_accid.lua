RegisterNetEvent('base_player:showID')
AddEventHandler('base_player:showID', function(accountID)
    if not accountID then
        return
    end

    TriggerEvent('chatMessage', "Account ID ", {255, 0, 0}, "Your Account ID is: " .. accountID ..". Note this down in case of a lost account!")
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    Wait(5000)
    TriggerServerEvent('base_player:login')
end)

RegisterNetEvent('qb-accountid:openLookupMenu')
AddEventHandler('qb-accountid:openLookupMenu', function()
    local options = {
        {
            title = 'Lookup Account ID',
            description = 'Enter an account ID to find the license',
            icon = 'fa-solid fa-magnifying-glass',
            onSelect = function()
                local input = lib.inputDialog('Enter Account ID', {'Account ID'})
                if not input or not input[1] or tonumber(input[1]) == nil then
                    lib.notify({title = 'Error', description = 'Invalid Account ID!', type = 'error'})
                    return
                end
                
                local accountID = tonumber(input[1])
                lib.callback('qb-accountid:lookupLicense', false, function(license)
                    if license then
                        lib.registerContext({
                            id = 'account_id_result_menu',
                            title = 'License Lookup Result',
                            options = {
                                {
                                    title = 'License: ' .. license,
                                    description = 'Click to copy license to clipboard',
                                    icon = 'fa-solid fa-clipboard',
                                    onSelect = function()
                                        lib.setClipboard(license)
                                        lib.notify({title = 'Success', description = 'License copied to clipboard!', type = 'success'})
                                    end
                                }
                            }
                        })
                        lib.showContext('account_id_result_menu')
                    else
                        lib.notify({title = 'Error', description = 'No license found for this ID!', type = 'error'})
                    end
                end, accountID)
            end
        }
    }

    lib.registerContext({
        id = 'account_id_lookup_menu',
        title = 'Account ID Lookup',
        options = options
    })

    lib.showContext('account_id_lookup_menu')
end)



