RegisterNetEvent("playtime:receivePlaytime", function(playTime)
    if playTime then
        lib.notify({
            title = "Playtime",
            description = "You have played for " .. playTime .. ".",
            type = "success"
        })
    else
        lib.notify({
            title = "Playtime",
            description = "Could not retrieve your playtime. Contact an admin.",
            type = "error"
        })
    end
end)

RegisterCommand("playtime", function()
    TriggerServerEvent("playtime:requestPlaytime")
end, false)
