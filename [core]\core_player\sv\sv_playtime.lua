local Config = {
    PossiblePaths = {
        '../../txData/default/data/playersDB.json',
        '../../../txData/default/data/playersDB.json',
        '../../../../txData/default/data/playersDB.json',
        './txData/default/data/playersDB.json'
    }
}

local function findPlayersDBFile()
    local resourcePath = GetResourcePath(GetCurrentResourceName())
    for _, relativePath in ipairs(Config.PossiblePaths) do
        local fullPath = resourcePath .. '/' .. relativePath
        local file = io.open(fullPath, 'r')
        if file then
            file:close()
            return fullPath
        end
    end
    return nil
end

local playersDBPath = findPlayersDBFile()

local function getPlayerPlaytime(source)
    if not playersDBPath then
        return nil
    end

    local license = GetPlayerIdentifierByType(source, "license")

    if not license then
        return nil
    end

    license = license:gsub("license:", "")

    local file = io.open(playersDBPath, 'r')
    if not file then
        return nil
    end

    local content = file:read('*all')
    file:close()

    local success, data = pcall(json.decode, content)
    if not success or not data or not data.players then
        return nil
    end

    for _, playerData in ipairs(data.players) do
        if playerData.license == license then
            return playerData.playTime
        end
    end
    return nil
end

exports("GetPlayerPlaytime", function(source)
    return getPlayerPlaytime(source)
end)