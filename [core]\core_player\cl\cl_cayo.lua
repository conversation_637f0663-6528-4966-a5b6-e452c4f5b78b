-- <PERSON>.CreateThread(function()
--     local cayoCoords = {
--         {x = 4800.85, y = -6159.22, z = 0.0},
--         {x = 6420.60, y = -5169.87, z = 37.43},
--     }

--     for _, coords in ipairs(cayoCoords) do
--         local blip = AddBlipForCoord(coords.x, coords.y, coords.z)

--         SetBlipSprite(blip, 1)
--         SetBlipAlpha(blip, 0)
--         SetBlipScale(blip, 0.1)
--         SetBlipAsShortRange(blip, true)
--     end
-- end)