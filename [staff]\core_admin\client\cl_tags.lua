local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local nameTagsActive = false
local gamertagCache = {}
local playerGroups = {}
local playerAccountIds = {}
local playersWithTagsActive = {}
local lastUpdate = 0

CreateThread(function()
    while QBCore.Functions.GetPlayerData() == nil do
        Wait(100)
    end
    PlayerData = QBCore.Functions.GetPlayerData()
    print('[TAGS] QBCore Loaded - Player Group: ' .. tostring(PlayerData.metadata.group))
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    print('[TAGS] Player Loaded - Group: ' .. tostring(PlayerData.metadata.group))
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
end)

RegisterNetEvent('tags:receivePlayerGroups')
AddEventHandler('tags:receivePlayerGroups', function(groups, accountIds)
    playerGroups = groups
    playerAccountIds = accountIds or {}
    print('[TAGS] Received player groups: ' .. json.encode(groups))
    print('[TAGS] Received account IDs: ' .. json.encode(playerAccountIds))
end)

RegisterNetEvent('tags:receiveActiveTagPlayers')
AddEventHandler('tags:receiveActiveTagPlayers', function(activePlayers)
    playersWithTagsActive = activePlayers or {}
    print('[TAGS] Received active tag players: ' .. json.encode(playersWithTagsActive))

    if nameTagsActive then
        print('[TAGS] Updating nametag colors for active player')
        for playerId, cache in pairs(gamertagCache) do
            UpdatePlayerNameTag(playerId)
        end
    else
        print('[TAGS] Ignoring nametag update - this player has nametags disabled')
    end
end)

function IsPlayerAdmin(group)
    return group == 'admin'
end

function GetPlayerGroup(serverId)
    return playerGroups[tostring(serverId)] or 'user'
end

function GetPlayerAccountId(serverId)
    return playerAccountIds[tonumber(serverId)] or serverId
end

function GetStarCount(group)
    return Config.TagStars[group] or 0
end

function GetGroupColors(group)
    return Config.GroupColors[group] or Config.GroupColors['user']
end

function DoesPlayerHaveTagsActive(serverId)
    return playersWithTagsActive[tostring(serverId)] == true
end

RegisterKeyMapping('names', 'Toggle Admin Name Tags', 'keyboard', 'F2')

RegisterCommand('names', function()
    ToggleNameTags()
end, false)


function ToggleNameTags()
    print('[TAGS] Toggle called - PlayerData: ' .. tostring(PlayerData ~= nil))

    if not PlayerData then
        print('[TAGS] No player data')
        lib.notify({
            title = 'TAGS',
            description = 'Player data not loaded',
            type = 'error'
        })
        return
    end

    if nameTagsActive then
        nameTagsActive = false
        print('[TAGS] Disabling name tags for this player only')
        lib.notify({
            title = 'Names',
            description = 'Names off',
            type = 'error'
        })
        TriggerServerEvent('tags:playerToggled', false)
        ClearAllNameTags()
    else
        nameTagsActive = true
        print('[TAGS] Enabling name tags for this player only')
        lib.notify({
            title = 'Names',
            description = 'Names on',
            type = 'success'
        })
        TriggerServerEvent('tags:playerToggled', true)
        StartNameTags()
    end
end

function ClearAllNameTags()
    for playerId, cache in pairs(gamertagCache) do
        if cache.gamertag then
            RemoveMpGamerTag(cache.gamertag)
        end
    end
    gamertagCache = {}
end

function UpdatePlayerNameTag(playerId)
    local playerPed = GetPlayerPed(playerId)
    local serverId = GetPlayerServerId(playerId)
    local playerName = GetPlayerName(playerId)

    if not serverId or not playerName or not DoesEntityExist(playerPed) then
        return
    end

    local playerGroup = GetPlayerGroup(serverId)
    local starCount = 5

    local playerHasTagsActive = DoesPlayerHaveTagsActive(serverId)
    local isLocalPlayer = playerId == PlayerId()

    local nameColor, starColor, healthColor

    if isLocalPlayer and nameTagsActive then
        nameColor = Config.Colors.Red
    elseif playerHasTagsActive then
        nameColor = Config.Colors.Red
    else
        nameColor = Config.Colors.White
    end

    starColor = Config.Colors.White
    healthColor = Config.Colors.Red

    local accountId = GetPlayerAccountId(serverId)
    local displayText = accountId .. ' - ' .. playerName

    if gamertagCache[playerId] and gamertagCache[playerId].gamertag then
        RemoveMpGamerTag(gamertagCache[playerId].gamertag)
    end

    local gamertag = CreateMpGamerTag(playerPed, displayText, false, false, '', false)

    if gamertag then
        SetMpGamerTagVisibility(gamertag, 0, true)
        SetMpGamerTagColour(gamertag, 0, nameColor)

        SetMpGamerTagVisibility(gamertag, 2, true)
        SetMpGamerTagAlpha(gamertag, 2, 255)
        SetMpGamerTagHealthBarColour(gamertag, healthColor)

        SetMpGamerTagWantedLevel(gamertag, starCount)
        if starCount > 0 then
            SetMpGamerTagVisibility(gamertag, 7, true)
            SetMpGamerTagColour(gamertag, 7, starColor)
        else
            SetMpGamerTagVisibility(gamertag, 7, false)
        end

        if NetworkIsPlayerTalking(playerId) then
            SetMpGamerTagVisibility(gamertag, 9, true)
        else
            SetMpGamerTagVisibility(gamertag, 9, false)
        end

        gamertagCache[playerId] = {
            gamertag = gamertag,
            lastUpdate = GetGameTimer()
        }

        return true
    end

    return false
end

function StartNameTags()
    print('[TAGS] Starting name tags system for this player only')

    TriggerServerEvent('tags:requestPlayerGroups')
    TriggerServerEvent('tags:requestActiveTagPlayers')

    CreateThread(function()
        Wait(500)
        local updateInterval = 0
        local cleanupInterval = 0

        while nameTagsActive do
            local currentTime = GetGameTimer()

            if currentTime - updateInterval >= 2000 then
                updateInterval = currentTime
                local activePlayers = GetActivePlayers()
                local currentPlayers = {}

                for _, playerId in ipairs(activePlayers) do
                    currentPlayers[playerId] = true
                    if not gamertagCache[playerId] then
                        UpdatePlayerNameTag(playerId)
                    end
                end

                if currentTime - cleanupInterval >= 5000 then
                    cleanupInterval = currentTime
                    for playerId, cache in pairs(gamertagCache) do
                        if not currentPlayers[playerId] then
                            if cache.gamertag then
                                RemoveMpGamerTag(cache.gamertag)
                            end
                            gamertagCache[playerId] = nil
                        end
                    end
                end
            end

            for playerId, cache in pairs(gamertagCache) do
                if cache.gamertag then
                    if NetworkIsPlayerTalking(playerId) then
                        SetMpGamerTagVisibility(cache.gamertag, 9, true)
                    else
                        SetMpGamerTagVisibility(cache.gamertag, 9, false)
                    end
                end
            end

            Wait(500)
        end

        print('[TAGS] Name tags disabled for this player, cleaning up their view only')
        ClearAllNameTags()
    end)
end

AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        print('[TAGS] Resource stopping, cleaning up')
        ClearAllNameTags()
    end
end)

CreateThread(function()
    Wait(2000)
    print('[TAGS] Resource loaded successfully')
end)
