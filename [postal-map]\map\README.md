<h1 align="center">OULSEN</h1>
<h3 align="center">FIVEM Satellite map with custom postals</h3>

I never intended to release this postal-map to the public but because someone somehow got the files for it and is selling it for money, I feel that I have no other option than to release it to the community. 

Just to emphasize, I am the original creator of this map, with the custom postals, all road names, road signs and poi’s. This map, and a couple of variations for it, were created 2 years ago. One of the variations was eventually used in DOJ, I believe they are still using it.I am releasing specifically this variation, because someone is selling it for money and they did not create it.

The map is using the high resolution sat map from this forum post: https://gtaforums.com/topic/595113-high-resolution-maps-satellite-roadmap-atlas/
The minimap is streamed with the code from https://github.com/ArduousDev/MiniMap

I’m releasing the files for the community to use and modify/edit. If you want to change the map, add logos or do whatever… you can! In the github there is a folder called DDS and in here are the separate files for you to edit. You can use PAINT.NET to edit the DDS files and save these files. Use OPENIV to swap the textures for the .ytd files. Use the DDS format instead of PNG, because PNG files don’t get the best quality in game.

If you want to use the nearest-postal script https://forum.cfx.re/t/release-nearest-postal-script/ , you can use the oulsen_satmap_postals.json which is included in the Github. The nearest-postal script is not included at this time, but you can download that separately. 

## Install
1. Upload oulsen_satmap folder to your resources folder on your server
2. Add oulsen_satmap to the server.cfg file (start or ensure).
3. Optional, change the map to the nogrid version. Just copy the files in the NoGrid folder and paste/overwrite these files in the stream folder of the resource.
4. Start the resource or reboot your server

[Correction] 29-03-2021 The was not created with the ones fro DieLikeKane , it was created with the high resolution sat map from this forum post: https://gtaforums.com/topic/595113-high-resolution-maps-satellite-roadmap-atlas/
