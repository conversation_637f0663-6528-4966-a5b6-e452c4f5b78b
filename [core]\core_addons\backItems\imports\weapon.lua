local CBackItem = require 'backItems.imports.backitem'
local Utils = require 'backItems.imports.utils'
local ox_items = exports.ox_inventory:Items()

--- @class CBackWeapon : CBackItem
--- @field new fun(self: self, serverId: number, itemData: ItemData
--- @field constructor fun(self: self, serverId: number, itemData: ItemData)
--- @field create fun(self: self, model?: number)
--- @field getComponents fun(self: self, model?: number)
--- @field attachComponents fun(self: self)
--- @field checkVarMod fun(self: self)
--- @field hasFlashLight fun(self: self): boolean
--- @field varMod number | nil
--- @field hadClip boolean
--- @field weaponComponents table<string | number>
local BackWeapon = lib.class('BackWeapon', CBackItem)

function BackWeapon:constructor(serverId, itemData)
    self:super(serverId, itemData)

    if itemData.model then return end

    -- Get weapon components before creating the weapon object
    self:getComponents()

    pcall(lib.requestWeaponAsset, itemData.hash, 10000, 31, 0)

    if self.varMod and not HasModelLoaded(self.varMod) then
        pcall(lib.requestModel, self.varMod, 10000)
    end

    local showDefault = true

    if self.varMod and self.hadClip then
        showDefault = false
    end

    self.object = CreateWeaponObject(itemData.hash, 0, 0.0, 0.0, 0.0, showDefault, 1.0, self.varMod or 0)

    -- Apply weapon components to show attachments
    if self.weaponComponents and #self.weaponComponents > 0 then
        for i = 1, #self.weaponComponents do
            GiveWeaponComponentToWeaponObject(self.object, self.weaponComponents[i])
        end
    end

    if itemData.tint then
        SetWeaponObjectTintIndex(self.object, itemData.tint)
    end

    if itemData.flashlight then
        SetCreateWeaponObjectLightSource(self.object, true)
        Wait(0)
    end

    RemoveWeaponAsset(itemData.hash)

    self:attach()
end

function BackWeapon:checkVarMod()
    local components = self.itemData.components

    if not components or type(components) ~= "table" or #components == 0 then
        return
    end

    for i = 1, #components do
        local componentName = type(components[i]) == "table" and components[i].name or components[i]
        local component = ox_items[componentName]

        if component and (component.type == 'skin' or component.type == 'upgrade') then
            if component.client and component.client.component then
                local weaponComp = component.client.component
                for j = 1, #weaponComp do
                    local weaponComponent = weaponComp[j]
                    if DoesWeaponTakeWeaponComponent(self.itemData.hash, weaponComponent) then
                        self.varMod = GetWeaponComponentTypeModel(weaponComponent)
                        break -- Found the variant mod, no need to continue
                    end
                end
            end
        end
    end
end

function BackWeapon:getComponents()
    local itemData = self.itemData
    local name, hash, components = itemData.name, itemData.hash, itemData.components
    local weaponComponents = {}
    local amount = 0
    self.hadClip = false

    -- Initialize weaponComponents table
    self.weaponComponents = {}

    -- Check for variant modifications first
    self:checkVarMod()

    -- If no components, just add default clip and return
    if not components or type(components) ~= "table" or #components == 0 then
        weaponComponents[1] = joaat(('COMPONENT_%s_CLIP_01'):format(name:sub(8)))
        self.weaponComponents = weaponComponents
        return
    end

    -- Process each component
    for i = 1, #components do
        local componentName = type(components[i]) == "table" and components[i].name or components[i]
        local weaponComp = ox_items[componentName]

        if weaponComp and weaponComp.client and weaponComp.client.component then
            for j = 1, #weaponComp.client.component do
                local weaponComponent = weaponComp.client.component[j]
                if DoesWeaponTakeWeaponComponent(hash, weaponComponent) and self.varMod ~= weaponComponent then
                    amount = amount + 1
                    weaponComponents[amount] = weaponComponent

                    if weaponComp.type == 'magazine' then
                        self.hadClip = true
                    end

                    break
                end
            end
        end
    end

    -- Add default clip if no magazine component was found
    if not self.hadClip then
        amount = amount + 1
        weaponComponents[amount] = joaat(('COMPONENT_%s_CLIP_01'):format(name:sub(8)))
    end

    self.weaponComponents = weaponComponents
end

return BackWeapon
