-- FDG Map Client Loader (Stream Team Best Team)
-- Last Edit: Bungle 12/05/2020

-- Custom Visualsettings.dat

function stringsplit(inputstr, sep)
	if sep == nil then
		sep = "%s"
	end

	local t={} ; i=1

	for str in string.gmatch(inputstr, "([^"..sep.."]+)") do
		t[i] = str
		i = i + 1
	end

	return t
end

local function starts_with(str, start)
   return str:sub(1, #start) == start
end

CreateThread(function()
	local settingsFile = LoadResourceFile(GetCurrentResourceName(), "visualsettings.dat")
	if not settingsFile then return end

	local lines = stringsplit(settingsFile, "\n")
	local setVisualFloat = GetHashKey('SET_VISUAL_SETTING_FLOAT') & 0xFFFFFFFF

	for _, line in ipairs(lines) do
		if not starts_with(line, '#') and not starts_with(line, '//') and line ~= "" and line ~= " " and #line > 1 then
			line = line:gsub("%s+", " ")
			local setting = stringsplit(line, " ")

			if setting[1] and setting[2] and tonumber(setting[2]) and setting[1] ~= 'weather.CycleDuration' then
				Citizen.InvokeNative(setVisualFloat, setting[1], tonumber(setting[2]))
			end
		end
	end
end)

CreateThread(function()
    while true do
        Wait(1000)
        ExpandWorldLimits(-15000.0, -15000.0, -12000.0)
        ExpandWorldLimits(15000.0, 15000.0, 12000.0)
    end
end)