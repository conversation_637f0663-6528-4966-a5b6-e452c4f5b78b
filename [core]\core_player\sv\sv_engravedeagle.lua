local QBCore = exports['qb-core']:GetCoreObject()

local allowedWeapons = {
    ["WEAPON_PRINTSTREAMPISTOL50"] = true,
    ["WEAPON_GUNGIRL"] = true
}

QBCore.Functions.CreateUseableItem("deagleengraver", function(source)
    local player = QBCore.Functions.GetPlayer(source)
    if not player then return end

    local weaponCount = exports.ox_inventory:Search(source, 'count', "WEAPON_PISTOL50")

    if weaponCount and weaponCount > 0 then
        lib.callback.await("base_player:useEngraver", source)
    else
        lib.callback.await("base_player:engraveFailed2", source)
    end
end)

lib.callback.register("base_player:checkWeapon", function()
    local src = source
    local player = QBCore.Functions.GetPlayer(src)
    if not player then return end

    local hasWeapon = exports.ox_inventory:Search(src, 1, "WEAPON_PISTOL50")
    local hasEngraver = exports.ox_inventory:Search(src, 'count', "deagleengraver") > 0

    if hasWeapon and hasEngraver then
        lib.callback.await("base_player:openEngravingMenu", src)
    else
        lib.callback.await("base_player:engraveFailed", src)
    end
end)

lib.callback.register("base_player:engraveWeapon", function(source, chosenWeapon)
    local player = QBCore.Functions.GetPlayer(source)
    if not player then return false end

    if not allowedWeapons[chosenWeapon] then
        exports.qbx_core:ExploitBan(source, "Attempted to trigger engraving with invalid weapon: " .. chosenWeapon)
        return false
    end

    local hasWeaponPistol50 = exports.ox_inventory:Search(source, 'count', "WEAPON_PISTOL50") > 0
    local hasEngraver = exports.ox_inventory:Search(source, 'count', "deagleengraver") > 0

    if hasWeaponPistol50 and hasEngraver then
        exports.ox_inventory:RemoveItem(source, "WEAPON_PISTOL50", 1)
        exports.ox_inventory:RemoveItem(source, "deagleengraver", 1)
        exports.ox_inventory:AddItem(source, chosenWeapon, 1)

        return true
    else
        exports.qbx_core:ExploitBan(source, "Attempted to trigger engraving with missing items")
        return false
    end
end)