Chat._cache = {
	active = false,
	activating = false,
	hidden = true,
	loaded = false,
}

RegisterCommand('chat', function()
	if not Chat._cache.active then
		Chat._cache.active = true
		Chat._cache.activating = true

		SendNUIMessage({ type = 'ON_OPEN' })

		Wait(25)
		SetNuiFocus(true)
		Chat._cache.activating = false
	end
end, false)

Citizen.CreateThread(function()
	while true do
		Wait(0)
		
		if IsControlJustPressed(0, 245) then -- T key
			if not Chat._cache.active then
				Chat._cache.active = true
				Chat._cache.activating = true

				SendNUIMessage({ type = 'ON_OPEN' })

				Wait(25)
				SetNuiFocus(true)
				Chat._cache.activating = false
			end
		end
	end
end)

RegisterNUICallback('chatResult', function(data, cb)
	Chat._cache.active = false
	SetNuiFocus(false)
	if (data.canceled) then return cb('ok') end

	local message = (data.message:sub(1, 1) == '/' and data.message:sub(2) or data.message)
	ExecuteCommand(message)

	cb('ok')
end)

RegisterNUICallback('loaded', function(data, cb)
	TriggerServerEvent('fx_chat:init')
	Chat.refresh()
	Chat._cache.loaded = true
	cb('ok')
end)

RegisterNUICallback('toggleSettings', function(data, cb)
	SetNuiFocus(true, data.state)
	cb('ok')
end)

Citizen.CreateThread(function()
	SetTextChatEnabled(false)

	while true do
		Wait(0)

		if Chat._cache.loaded then
			local shouldBeHidden = false

			if IsScreenFadedOut() or IsPauseMenuActive() then
				shouldBeHidden = true
			end

			if (shouldBeHidden and not Chat._cache.hidden) or (not shouldBeHidden and Chat._cache.hidden) then
				Chat._cache.hidden = shouldBeHidden

				SendNUIMessage({
					type = 'ON_SCREEN_STATE_CHANGE',
					shouldHide = shouldBeHidden
				})
			end
		end
	end
end)

RegisterNetEvent('chat:chatMessage', Chat.message)
RegisterNetEvent('chat:addSuggestion', Chat.addSuggestion)
RegisterNetEvent('chat:removeSuggestion', Chat.removeSuggestion)
RegisterNetEvent('chat:addMessage', Chat.push)
RegisterNetEvent('chat:clear', Chat.clear)
RegisterNetEvent('chat:addSuggestions', Chat.addSuggestions)
RegisterNetEvent('__cfx_internal:serverPrint')
RegisterNetEvent('_fx_chat:messageEntered')
AddEventHandler('onClientResourceStart', Chat.refresh)
AddEventHandler('onClientResourceStop', Chat.refresh)
