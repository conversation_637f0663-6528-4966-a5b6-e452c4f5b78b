local Config = {
    deformationMultiplier = -1,
    deformationExponent = 0.4,
    collisionDamageExponent = 0.6,
    damageFactorEngine = 2.0,
    damageFactorBody = 3.0,
    damageFactorPetrolTank = 64.0,
    engineDamageExponent = 0.6,
    weaponsDamageMultiplier = 2.0,
    degradingHealthSpeedFactor = 10,
    cascadingFailureSpeedFactor = 8.0,
    degradingFailureThreshold = 800.0,
    cascadingFailureThreshold = 360.0,
    engineSafeGuard = 100.0,
    torqueMultiplierEnabled = true,
    limpMode = false,
    limpModeMultiplier = 0.19,
    preventVehicleFlip = false,
    sundayDriver = false,
    sundayDriverAcceleratorCurve = 7.5,
    sundayDriverBrakeCurve = 5.0,
    compatibilityMode = false,
    randomTireBurstInterval = 0,
    classDamageMultiplier = {
        [0] = 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
        0.25, 0.7, 0.25, 1.0, 1.0, 1.0, 0.5, 1.0,
        1.0, 1.0, 0.75, 0.75, 1.0, 1.0
    }
}

local pedInSameVehicleLast, vehicle, lastVehicle, vehicleClass
local fCollisionDamageMult, fDeformationDamageMult, fEngineDamageMult, fBrakeForce = 0.0, 0.0, 0.0, 1.0
local isBrakingForward, isBrakingReverse = false, false
local healthEngineLast, healthEngineCurrent, healthEngineNew = 1000.0, 1000.0, 1000.0
local healthBodyLast, healthBodyCurrent, healthBodyNew = 1000.0, 1000.0, 1000.0
local healthPetrolTankLast, healthPetrolTankCurrent, healthPetrolTankNew = 1000.0, 1000.0, 1000.0
local tireBurstLuckyNumber

math.randomseed(GetGameTimer())
local tireBurstMaxNumber = Config.randomTireBurstInterval * 1200
if Config.randomTireBurstInterval ~= 0 then tireBurstLuckyNumber = math.random(tireBurstMaxNumber) end

local function isPedDrivingAVehicle()
    local ped = PlayerPedId()
    vehicle = GetVehiclePedIsIn(ped, false)
    if IsPedInAnyVehicle(ped, false) and GetPedInVehicleSeat(vehicle, -1) == ped then
        local class = GetVehicleClass(vehicle)
        if class ~= 15 and class ~= 16 and class ~= 21 and class ~= 13 then return true end
    end
    return false
end

local function tireBurstLottery()
    if math.random(tireBurstMaxNumber) == tireBurstLuckyNumber and GetVehicleTyresCanBurst(vehicle) then
        local numWheels = GetVehicleNumberOfWheels(vehicle)
        local affectedTire = (numWheels == 2) and (math.random(2) - 1) * 4 or (numWheels == 4) and ((math.random(4) - 1) > 1 and math.random(4) + 2 or math.random(4)) or (numWheels == 6) and math.random(6) - 1 or 0
        SetVehicleTyreBurst(vehicle, affectedTire, false, 1000.0)
        tireBurstLuckyNumber = math.random(tireBurstMaxNumber)
    end
end

if Config.torqueMultiplierEnabled or Config.preventVehicleFlip or Config.limpMode then
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(1)
            if pedInSameVehicleLast then
                local factor = Config.torqueMultiplierEnabled and (healthEngineNew < 900 and (healthEngineNew + 200.0) / 1100 or 1.0) or 1.0
                if Config.limpMode and healthEngineNew < Config.engineSafeGuard + 5 then factor = Config.limpModeMultiplier end
                SetVehicleEngineTorqueMultiplier(vehicle, factor)
            end
            if Config.preventVehicleFlip and GetEntityRoll(vehicle) > 75.0 or GetEntityRoll(vehicle) < -75.0 and GetEntitySpeed(vehicle) < 2 then
                DisableControlAction(2, 59, true)
                DisableControlAction(2, 60, true)
            end
        end
    end)
end

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(50)
        local ped = PlayerPedId()
        if isPedDrivingAVehicle() then
            vehicle = GetVehiclePedIsIn(ped, false)
            vehicleClass = GetVehicleClass(vehicle)
            healthEngineCurrent, healthBodyCurrent, healthPetrolTankCurrent = GetVehicleEngineHealth(vehicle), GetVehicleBodyHealth(vehicle), GetVehiclePetrolTankHealth(vehicle)
            healthEngineNew, healthBodyNew, healthPetrolTankNew = healthEngineCurrent, healthBodyCurrent, healthPetrolTankCurrent
            
            if pedInSameVehicleLast then
                local damageDelta = math.max(healthEngineLast - healthEngineCurrent, healthBodyLast - healthBodyCurrent, healthPetrolTankLast - healthPetrolTankCurrent)
                healthEngineNew = healthEngineLast - damageDelta * Config.damageFactorEngine * Config.classDamageMultiplier[vehicleClass]

                if healthEngineNew < Config.cascadingFailureThreshold then
                    healthEngineNew = healthEngineNew - (0.1 * Config.cascadingFailureSpeedFactor)
                end

                if healthEngineNew < Config.engineSafeGuard then
                    healthEngineNew = Config.engineSafeGuard
                    SetVehicleUndriveable(vehicle, true)
                end
            else
                pedInSameVehicleLast = true
            end
            
            if healthEngineNew ~= healthEngineCurrent then SetVehicleEngineHealth(vehicle, healthEngineNew) end
            if healthBodyNew ~= healthBodyCurrent then SetVehicleBodyHealth(vehicle, healthBodyNew) end
            if healthPetrolTankNew ~= healthPetrolTankCurrent then SetVehiclePetrolTankHealth(vehicle, healthPetrolTankNew) end
            
            healthEngineLast, healthBodyLast, healthPetrolTankLast = healthEngineNew, healthBodyNew, healthPetrolTankNew
            lastVehicle = vehicle

            if healthEngineNew <= Config.cascadingFailureThreshold then
                StartVehicleAlarm(vehicle)
                SetVehicleEngineOn(vehicle, false, true, true)
            end

            if Config.randomTireBurstInterval ~= 0 and GetEntitySpeed(vehicle) > 10 then 
                tireBurstLottery()
            end
        else
            pedInSameVehicleLast = false
        end
    end
end)

CreateThread(function()
    while true do
        Wait(0)
        local ped = PlayerPedId()
        if IsPedInAnyVehicle(ped, false) then
            local vehicle = GetVehiclePedIsIn(ped, false)
            local isEngineRunning = GetIsVehicleEngineRunning(vehicle)
            if not isEngineRunning then
                DisableControlAction(0, 303, true)
                if IsControlJustPressed(0, 303) then
                    SetVehicleEngineOn(vehicle, false, true, true)
                end
            end
        end
    end
 end)
