Citizen.CreateThread(function()
    -- ====================================================================
    -- =--------------------- [GTA V: Single player] ---------------------=
    -- ====================================================================

    -- Michael: -802.311, 175.056, 72.8446
    Michael.LoadDefault()

    -- Simeon: -47.16170 -1115.3327 26.5
    Simeon.LoadDefault()

    -- <PERSON>'s aunt: -9.96562, -1438.54, 31.1015
    FranklinAunt.LoadDefault()

    -- <PERSON>.<PERSON>efault()

    -- Floyd: -1150.703, -1520.713, 10.633
    Floyd.LoadDefault()

    -- Trevor: 1985.48132, 3828.76757, 32.5
    <PERSON><PERSON><PERSON><PERSON><PERSON>.<PERSON><PERSON><PERSON><PERSON>ault()

    -- Ba<PERSON><PERSON> Mamas: -1388.0013, -618.41967, 30.819599
    BahamaMamas.Enable(true)

    -- Pillbox hospital: 307.1680, -590.807, 43.280
    PillboxHospital.Enable(false)

    -- Zancudo Gates (GTAO like): -1600.30100000, 2806.73100000, 18.79683000
    ZancudoGates.LoadDefault()

    -- Other
    Ammunations.LoadDefault()
    LesterFactory.LoadDefault()
    StripClub.LoadDefault()

    Graffitis.Enable(true)

    -- UFO
    UFO.Hippie.Enable(false) -- 2490.47729, 3774.84351, 2414.035
    UFO.Chiliad.Enable(false) -- 501.52880000, 5593.86500000, 796.23250000
    UFO.Zancudo.Enable(false) -- -2051.99463, 3237.05835, 1456.97021

    -- Red Carpet: 300.5927, 199.7589, 104.3776
    RedCarpet.Enable(false)

    -- North Yankton: 3217.697, -4834.826, 111.8152
    NorthYankton.Enable(false)

    -- ====================================================================
    -- =-------------------------- [GTA Online] --------------------------=
    -- ====================================================================
    GTAOApartmentHi1.LoadDefault() -- -35.31277 -580.4199 88.71221 (4 Integrity Way, Apt 30)
    GTAOApartmentHi2.LoadDefault() -- -1477.14 -538.7499 55.5264 (Dell Perro Heights, Apt 7)
    GTAOHouseHi1.LoadDefault() -- -169.286 486.4938 137.4436 (3655 Wild Oats Drive)
    GTAOHouseHi2.LoadDefault() -- 340.9412 437.1798 149.3925 (2044 North Conker Avenue)
    GTAOHouseHi3.LoadDefault() -- 373.023 416.105 145.7006 (2045 North Conker Avenue)
    GTAOHouseHi4.LoadDefault() -- -676.127 588.612 145.1698 (2862 Hillcrest Avenue)
    GTAOHouseHi5.LoadDefault() -- -763.107 615.906 144.1401 (2868 Hillcrest Avenue)
    GTAOHouseHi6.LoadDefault() -- -857.798 682.563 152.6529 (2874 Hillcrest Avenue)
    GTAOHouseHi7.LoadDefault() -- 120.5 549.952 184.097 (2677 Whispymound Drive)
    GTAOHouseHi8.LoadDefault() -- -1288 440.748 97.69459 (2133 Mad Wayne Thunder)
    GTAOHouseMid1.LoadDefault() -- 347.2686 -999.2955 -99.19622
    GTAOHouseLow1.LoadDefault() -- 261.4586 -998.8196 -99.00863

    -- ====================================================================
    -- =------------------------ [DLC: High life] ------------------------=
    -- ====================================================================
    HLApartment1.LoadDefault() -- -1468.14 -541.815 73.4442 (Dell Perro Heights, Apt 4)
    HLApartment2.LoadDefault() -- -915.811 -379.432 113.6748 (Richard Majestic, Apt 2)
    HLApartment3.LoadDefault() -- -614.86 40.6783 97.60007 (Tinsel Towers, Apt 42)
    HLApartment4.LoadDefault() -- -773.407 341.766 211.397 (EclipseTowers, Apt 3)
    HLApartment5.LoadDefault() -- -18.07856 -583.6725 79.46569 (4 Integrity Way, Apt 28)
    HLApartment6.LoadDefault() -- -609.56690000 51.28212000 -183.98080

    -- ====================================================================
    -- =-------------------------- [DLC: Heists] -------------------------=
    -- ====================================================================
    HeistCarrier.Enable(true) -- 3082.3117, -4717.1191, 15.2622
    HeistYacht.LoadDefault() -- -2043.974,-1031.582, 11.981

    -- ====================================================================
    -- =--------------- [DLC: Executives & Other Criminals] --------------=
    -- ====================================================================
    ExecApartment1.LoadDefault() -- -787.7805 334.9232 215.8384 (EclipseTowers, Penthouse Suite 1)
    ExecApartment2.LoadDefault() -- -773.2258 322.8252 194.8862 (EclipseTowers, Penthouse Suite 2)
    ExecApartment3.LoadDefault() -- -787.7805 334.9232 186.1134 (EclipseTowers, Penthouse Suite 3)

    -- ====================================================================
    -- =-------------------- [DLC: Finance  & Felony] --------------------=
    -- ====================================================================
    FinanceOffice1.LoadDefault() -- -141.1987, -620.913, 168.8205 (Arcadius Business Centre)
    FinanceOffice2.LoadDefault() -- -75.8466, -826.9893, 243.3859 (Maze Bank Building)
    FinanceOffice3.LoadDefault() -- -1579.756, -565.0661, 108.523 (Lom Bank)
    FinanceOffice4.LoadDefault() -- -1392.667, -480.4736, 72.04217 (Maze Bank West)

    -- ====================================================================
    -- =-------------------------- [DLC: Bikers] -------------------------=
    -- ====================================================================
    BikerCocaine.LoadDefault() -- Cocaine lockup: 1093.6, -3196.6, -38.99841
    BikerCounterfeit.LoadDefault() -- Counterfeit cash factory: 1121.897, -3195.338, -40.4025
    BikerDocumentForgery.LoadDefault() -- Document forgery: 1165, -3196.6, -39.01306
    BikerMethLab.LoadDefault() -- Meth lab: 1009.5, -3196.6, -38.99682
    BikerWeedFarm.LoadDefault() -- Weed farm: 1051.491, -3196.536, -39.14842
    BikerClubhouse1.LoadDefault() -- 1107.04, -3157.399, -37.51859
    BikerClubhouse2.LoadDefault() -- 998.4809, -3164.711, -38.90733

    -- ====================================================================
    -- =---------------------- [DLC: Import/Export] ----------------------=
    -- ====================================================================
    ImportCEOGarage1.LoadDefault() -- Arcadius Business Centre
    ImportCEOGarage2.LoadDefault() -- Maze Bank Building /!\ Do not load parts Garage1, Garage2 and Garage3 at the same time (overlaping issues)
    ImportCEOGarage3.LoadDefault() -- Lom Bank /!\ Do not load parts Garage1, Garage2 and Garage3 at the same time (overlaping issues)
    ImportCEOGarage4.LoadDefault() -- Maze Bank West /!\ Do not load parts Garage1, Garage2 and Garage3 at the same time (overlaping issues)
    ImportVehicleWarehouse.LoadDefault() -- Vehicle warehouse: 994.5925, -3002.594, -39.64699

    -- ====================================================================
    -- =------------------------ [DLC: Gunrunning] -----------------------=
    -- ====================================================================
    GunrunningBunker.LoadDefault() -- 892.6384, -3245.8664, -98.2645
    GunrunningYacht.LoadDefault() -- -1363.724, 6734.108, 2.44598

    -- ====================================================================
    -- =---------------------- [DLC: Smuggler's Run] ---------------------=
    -- ====================================================================
    SmugglerHangar.LoadDefault() -- -1267.0 -3013.135 -49.5

    -- ====================================================================
    -- =-------------------- [DLC: The Doomsday Heist] -------------------=
    -- ====================================================================
    DoomsdayFacility.LoadDefault()

    -- ====================================================================
    -- =----------------------- [DLC: After Hours] -----------------------=
    -- ====================================================================
    AfterHoursNightclubs.LoadDefault() -- -1604.664, -3012.583, -78.000

    -- ====================================================================
    -- =------------------- [DLC: Diamond Casino Resort] -----------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2060 then
        DiamondCasino.LoadDefault() -- 1100.000, 220.000, -50.000
        DiamondPenthouse.LoadDefault() -- 976.636, 70.295, 115.164
    end

    -- ====================================================================
    -- =------------------- [DLC: Los Santos Tuners] ---------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2372 then
        TunerGarage.LoadDefault() -- -1350.0, 160.0, -100.0
        TunerMethLab.LoadDefault() -- 981.9999, -143.0, -50.0
        TunerMeetup.LoadDefault() -- -2000.0, 1113.211, -25.36243
    end

    -- ====================================================================
    -- =------------------- [DLC: Los Santos The Contract] ---------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2545 then
        MpSecurityGarage.LoadDefault() -- -1071.4387, -77.033875, -93.525505
        MpSecurityMusicRoofTop.LoadDefault() -- -592.6896, 273.1052, 116.302444
        MpSecurityStudio.LoadDefault() -- -1000.7252, -70.559875, -98.10669
        MpSecurityBillboards.LoadDefault() -- -592.6896, 273.1052, 116.302444
        MpSecurityOffice1.LoadDefault() -- -1021.86084, -427.74564, 68.95764
        MpSecurityOffice2.LoadDefault() -- 383.4156, -59.878227, 108.4595
        MpSecurityOffice3.LoadDefault() -- -1004.23035, -761.2084, 66.99069
        MpSecurityOffice4.LoadDefault() -- -587.87213, -716.84937, 118.10156
    end

    -- ====================================================================
    -- =------------------- [DLC: The Criminal Enterprise] ---------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2699 then
        CriminalEnterpriseSmeonFix.LoadDefault() -- -50.2248, -1098.8325, 26.049742
        CriminalEnterpriseVehicleWarehouse.LoadDefault() -- 800.13696, -3001.4297, -65.14074
        CriminalEnterpriseWarehouse.LoadDefault() -- 849.1047, -3000.209, -45.974354
    end

    -- ====================================================================
    -- =------------------- [DLC: Los Santos Drug Wars] ------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2802 then
        DrugWarsFreakshop.LoadDefault() -- 570.9713, -420.0727, -70.000
        DrugWarsGarage.LoadDefault() -- 519.2477, -2618.788, -50.000
        DrugWarsLab.LoadDefault() -- 483.4252, -2625.071, -50.000
    end

    -- ====================================================================
    -- =------------------- [DLC: San Andreas Mercenaries] ---------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 2944 then
        MercenariesClub.LoadDefault() -- 1202.407, -3251.251, -50.000
        MercenariesLab.LoadDefault() -- -1916.119, 3749.719, -100.000
        MercenariesFixes.LoadDefault()
    end

    -- ====================================================================
    -- =------------------- [DLC: The Chop Shop] -------------------------=
    -- ====================================================================
    if GetGameBuildNumber() >= 3095 then
        ChopShopCargoShip.LoadDefault() -- -344.4349, -4062.832, 17.000
        ChopShopCartelGarage.LoadDefault() -- 1220.133, -2277.844, -50.000
        ChopShopLifeguard.LoadDefault() -- -1488.153, -1021.166, 5.000
        ChopShopSalvage.LoadDefault() -- 1077.276, -2274.876, -50.000
    end

end)

-- FDG Loaders

-- IPL Loaders

-- Bunker/Facility

Citizen.CreateThread(function()

    RequestIpl("xm_siloentranceclosed_x17")
	RequestIpl("sm_smugdlc_interior_placement")
	RequestIpl("sm_smugdlc_interior_placement_interior_0_smugdlc_int_01_milo_")
	RequestIpl("xm_x17dlc_int_placement")

	RequestIpl("xm_x17dlc_int_placement_interior_0_x17dlc_int_base_ent_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_1_x17dlc_int_base_loop_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_2_x17dlc_int_bse_tun_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_3_x17dlc_int_base_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_4_x17dlc_int_facility_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_5_x17dlc_int_facility2_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_6_x17dlc_int_silo_01_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_7_x17dlc_int_silo_02_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_8_x17dlc_int_sub_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_9_x17dlc_int_01_milo_")

	RequestIpl("xm_x17dlc_int_placement_interior_10_x17dlc_int_tun_straight_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_11_x17dlc_int_tun_slope_flat_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_12_x17dlc_int_tun_flat_slope_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_13_x17dlc_int_tun_30d_r_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_14_x17dlc_int_tun_30d_l_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_15_x17dlc_int_tun_straight_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_16_x17dlc_int_tun_straight_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_17_x17dlc_int_tun_slope_flat_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_18_x17dlc_int_tun_slope_flat_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_19_x17dlc_int_tun_flat_slope_milo_")

	RequestIpl("xm_x17dlc_int_placement_interior_20_x17dlc_int_tun_flat_slope_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_21_x17dlc_int_tun_30d_r_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_22_x17dlc_int_tun_30d_r_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_23_x17dlc_int_tun_30d_r_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_24_x17dlc_int_tun_30d_r_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_25_x17dlc_int_tun_30d_l_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_26_x17dlc_int_tun_30d_l_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_27_x17dlc_int_tun_30d_l_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_28_x17dlc_int_tun_30d_l_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_29_x17dlc_int_tun_30d_l_milo_")

	RequestIpl("xm_x17dlc_int_placement_interior_30_v_apart_midspaz_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_31_v_studio_lo_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_32_v_garagem_milo_")
	--RequestIpl("xm_x17dlc_int_placement_interior_33_x17dlc_int_02_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_34_x17dlc_int_lab_milo_")
	RequestIpl("xm_x17dlc_int_placement_interior_35_x17dlc_int_tun_entry_milo_")
end)


-- Cayo Perico

Citizen.CreateThread(function()
	-- Smooth Deep Oceans & Remove Snow at Islands
	SetDeepOceanScaler(0.0)
	SetZoneEnabled(GetZoneFromNameId("PrLog"), false)

    RequestIpl("h4_mph4_terrain_occ_09")
    RequestIpl("h4_mph4_terrain_occ_06")
    RequestIpl("h4_mph4_terrain_occ_05")
    RequestIpl("h4_mph4_terrain_occ_01")
    RequestIpl("h4_mph4_terrain_occ_00")
    RequestIpl("h4_mph4_terrain_occ_08")
    RequestIpl("h4_mph4_terrain_occ_04")
    RequestIpl("h4_mph4_terrain_occ_07")
    RequestIpl("h4_mph4_terrain_occ_03")
    RequestIpl("h4_mph4_terrain_occ_02")
    RequestIpl("h4_islandx_terrain_04")
    RequestIpl("h4_islandx_terrain_05_slod")
    RequestIpl("h4_islandx_terrain_props_05_d_slod")
    RequestIpl("h4_islandx_terrain_02")
    RequestIpl("h4_islandx_terrain_props_05_a_lod")
    RequestIpl("h4_islandx_terrain_props_05_c_lod")
    RequestIpl("h4_islandx_terrain_01")
    RequestIpl("h4_mph4_terrain_04")
    RequestIpl("h4_mph4_terrain_06")
    RequestIpl("h4_islandx_terrain_04_lod")
    RequestIpl("h4_islandx_terrain_03_lod")
    RequestIpl("h4_islandx_terrain_props_06_a")
    RequestIpl("h4_islandx_terrain_props_06_a_slod")
    RequestIpl("h4_islandx_terrain_props_05_f_lod")
    RequestIpl("h4_islandx_terrain_props_06_b")
    RequestIpl("h4_islandx_terrain_props_05_b_lod")
    RequestIpl("h4_mph4_terrain_lod")
    RequestIpl("h4_islandx_terrain_props_05_e_lod")
    RequestIpl("h4_islandx_terrain_05_lod")
    RequestIpl("h4_mph4_terrain_02")
    RequestIpl("h4_islandx_terrain_props_05_a")
    RequestIpl("h4_mph4_terrain_01_long_0")
    RequestIpl("h4_islandx_terrain_03")
    RequestIpl("h4_islandx_terrain_props_06_b_slod")
    RequestIpl("h4_islandx_terrain_01_slod")
    RequestIpl("h4_islandx_terrain_04_slod")
    RequestIpl("h4_islandx_terrain_props_05_d_lod")
    RequestIpl("h4_islandx_terrain_props_05_f_slod")
    RequestIpl("h4_islandx_terrain_props_05_c")
    RequestIpl("h4_islandx_terrain_02_lod")
    RequestIpl("h4_islandx_terrain_06_slod")
    RequestIpl("h4_islandx_terrain_props_06_c_slod")
    RequestIpl("h4_islandx_terrain_props_06_c")
    RequestIpl("h4_islandx_terrain_01_lod")
    RequestIpl("h4_mph4_terrain_06_strm_0")
    RequestIpl("h4_islandx_terrain_05")
    RequestIpl("h4_islandx_terrain_props_05_e_slod")
    RequestIpl("h4_islandx_terrain_props_06_c_lod")
    RequestIpl("h4_mph4_terrain_03")
    RequestIpl("h4_islandx_terrain_props_05_f")
    RequestIpl("h4_islandx_terrain_06_lod")
    RequestIpl("h4_mph4_terrain_01")
    RequestIpl("h4_islandx_terrain_06")
    RequestIpl("h4_islandx_terrain_props_06_a_lod")
    RequestIpl("h4_islandx_terrain_props_06_b_lod")
    RequestIpl("h4_islandx_terrain_props_05_b")
    RequestIpl("h4_islandx_terrain_02_slod")
    RequestIpl("h4_islandx_terrain_props_05_e")
    RequestIpl("h4_islandx_terrain_props_05_d")
    RequestIpl("h4_mph4_terrain_05")
    RequestIpl("h4_mph4_terrain_02_grass_2")
    RequestIpl("h4_mph4_terrain_01_grass_1")
    RequestIpl("h4_mph4_terrain_05_grass_0")
    RequestIpl("h4_mph4_terrain_01_grass_0")
    RequestIpl("h4_mph4_terrain_02_grass_1")
    RequestIpl("h4_mph4_terrain_02_grass_0")
    RequestIpl("h4_mph4_terrain_02_grass_3")
    RequestIpl("h4_mph4_terrain_04_grass_0")
    RequestIpl("h4_mph4_terrain_06_grass_0")
    RequestIpl("h4_mph4_terrain_04_grass_1")
    RequestIpl("island_distantlights")
    RequestIpl("island_lodlights")
    --RequestIpl("h4_yacht_strm_0")
    --RequestIpl("h4_yacht")
    --RequestIpl("h4_yacht_long_0")
    --RequestIpl("h4_islandx_yacht_01_lod")
    RequestIpl("h4_clubposter_palmstraxx")
    --RequestIpl("h4_islandx_yacht_02_int")
    --RequestIpl("h4_islandx_yacht_02")
    RequestIpl("h4_clubposter_moodymann")
    --RequestIpl("h4_islandx_yacht_01")
    --RequestIpl("h4_clubposter_keinemusik")
    --RequestIpl("h4_islandx_yacht_03")
    RequestIpl("h4_ch2_mansion_final")
    --RequestIpl("h4_islandx_yacht_03_int")
    --RequestIpl("h4_yacht_critical_0")
    --RequestIpl("h4_islandx_yacht_01_int")
    RequestIpl("h4_mph4_island_placement")
    RequestIpl("h4_islandx_mansion_vault")
    RequestIpl("h4_islandx_checkpoint_props")
    RequestIpl("h4_mph4_airstrip_interior_0_airstrip_hanger")
    RequestIpl("h4_islandairstrip_hangar_props_slod")
    RequestIpl("h4_se_ipl_01_lod")
    RequestIpl("h4_ne_ipl_00_slod")
    RequestIpl("h4_se_ipl_06_slod")
    RequestIpl("h4_ne_ipl_00")
    RequestIpl("h4_se_ipl_02")
    RequestIpl("h4_islandx_barrack_props_lod")
    RequestIpl("h4_se_ipl_09_lod")
    RequestIpl("h4_ne_ipl_05")
    RequestIpl("h4_mph4_island_se_placement")
    RequestIpl("h4_ne_ipl_09")
    RequestIpl("h4_islandx_mansion_props_slod")
    RequestIpl("h4_se_ipl_09")
    RequestIpl("h4_mph4_mansion_b")
    RequestIpl("h4_islandairstrip_hangar_props_lod")
    RequestIpl("h4_islandx_mansion_entrance_fence")
    RequestIpl("h4_nw_ipl_09")
    RequestIpl("h4_nw_ipl_02_lod")
    RequestIpl("h4_ne_ipl_09_slod")
    RequestIpl("h4_sw_ipl_02")
    RequestIpl("h4_islandx_checkpoint")
    RequestIpl("h4_islandxdock_water_hatch")
    RequestIpl("h4_nw_ipl_04_lod")
    RequestIpl("h4_islandx_maindock_props")
    RequestIpl("h4_beach")
    RequestIpl("h4_islandx_mansion_lockup_03_lod")
    RequestIpl("h4_ne_ipl_04_slod")
    RequestIpl("h4_mph4_island_nw_placement")
    RequestIpl("h4_ne_ipl_08_slod")
    RequestIpl("h4_nw_ipl_09_lod")
    RequestIpl("h4_se_ipl_08_lod")
    RequestIpl("h4_islandx_maindock_props_lod")
    RequestIpl("h4_se_ipl_03")
    RequestIpl("h4_sw_ipl_02_slod")
    RequestIpl("h4_nw_ipl_00")
    RequestIpl("h4_islandx_mansion_b_side_fence")
    RequestIpl("h4_ne_ipl_01_lod")
    RequestIpl("h4_se_ipl_06_lod")
    RequestIpl("h4_ne_ipl_03")
    RequestIpl("h4_islandx_maindock")
    RequestIpl("h4_se_ipl_01")
    RequestIpl("h4_sw_ipl_07")
    RequestIpl("h4_islandx_maindock_props_2")
    RequestIpl("h4_islandxtower_veg")
    RequestIpl("h4_mph4_island_sw_placement")
    RequestIpl("h4_se_ipl_01_slod")
    RequestIpl("h4_mph4_wtowers")
    RequestIpl("h4_se_ipl_02_lod")
    RequestIpl("h4_islandx_mansion")
    RequestIpl("h4_nw_ipl_04")
    RequestIpl("h4_islandx_mansion_lockup_01")
    RequestIpl("h4_islandx_barrack_props")
    RequestIpl("h4_nw_ipl_07_lod")
    RequestIpl("h4_nw_ipl_00_slod")
    RequestIpl("h4_sw_ipl_08_lod")
    RequestIpl("h4_islandxdock_props_slod")
    RequestIpl("h4_islandx_mansion_lockup_02")
    RequestIpl("h4_islandx_mansion_slod")
    RequestIpl("h4_sw_ipl_07_lod")
    RequestIpl("h4_islandairstrip_doorsclosed_lod")
    RequestIpl("h4_sw_ipl_02_lod")
    RequestIpl("h4_se_ipl_04_slod")
    RequestIpl("h4_islandx_checkpoint_props_lod")
    RequestIpl("h4_se_ipl_04")
    RequestIpl("h4_se_ipl_07")
    RequestIpl("h4_mph4_mansion_b_strm_0")
    RequestIpl("h4_nw_ipl_09_slod")
    RequestIpl("h4_se_ipl_07_lod")
    RequestIpl("h4_islandx_maindock_slod")
    RequestIpl("h4_islandx_mansion_lod")
    RequestIpl("h4_sw_ipl_05_lod")
    RequestIpl("h4_nw_ipl_08")
    RequestIpl("h4_islandairstrip_slod")
    RequestIpl("h4_nw_ipl_07")
    RequestIpl("h4_islandairstrip_propsb_lod")
    RequestIpl("h4_islandx_checkpoint_props_slod")
    RequestIpl("h4_aa_guns_lod")
    RequestIpl("h4_sw_ipl_06")
    RequestIpl("h4_islandx_maindock_props_2_slod")
    RequestIpl("h4_islandx_mansion_office")
    RequestIpl("h4_islandx_maindock_lod")
    RequestIpl("h4_mph4_dock")
    RequestIpl("h4_islandairstrip_propsb")
    RequestIpl("h4_islandx_mansion_lockup_03")
    RequestIpl("h4_nw_ipl_01_lod")
    RequestIpl("h4_se_ipl_05_slod")
    RequestIpl("h4_sw_ipl_01_lod")
    RequestIpl("h4_nw_ipl_05")
    RequestIpl("h4_islandxdock_props_2_lod")
    RequestIpl("h4_ne_ipl_04_lod")
    RequestIpl("h4_ne_ipl_01")
    RequestIpl("h4_beach_party_lod")
    RequestIpl("h4_islandx_mansion_lights")
    RequestIpl("h4_sw_ipl_00_lod")
    RequestIpl("h4_islandx_mansion_guardfence")
    RequestIpl("h4_beach_props_party")
    RequestIpl("h4_ne_ipl_03_lod")
    RequestIpl("h4_islandx_mansion_b")
    RequestIpl("h4_beach_bar_props")
    RequestIpl("h4_ne_ipl_04")
    RequestIpl("h4_sw_ipl_08_slod")
    RequestIpl("h4_islandxtower")
    RequestIpl("h4_se_ipl_00_slod")
    RequestIpl("h4_islandx_barrack_hatch")
    RequestIpl("h4_ne_ipl_06_slod")
    RequestIpl("h4_ne_ipl_03_slod")
    RequestIpl("h4_sw_ipl_09_slod")
    RequestIpl("h4_ne_ipl_02_slod")
    RequestIpl("h4_nw_ipl_04_slod")
    RequestIpl("h4_ne_ipl_05_lod")
    RequestIpl("h4_nw_ipl_08_slod")
    RequestIpl("h4_sw_ipl_05_slod")
    RequestIpl("h4_islandx_mansion_b_lod")
    RequestIpl("h4_ne_ipl_08")
    RequestIpl("h4_islandxdock_props")
    RequestIpl("h4_islandairstrip_doorsopen_lod")
    RequestIpl("h4_se_ipl_05_lod")
    RequestIpl("h4_islandxcanal_props_slod")
    RequestIpl("h4_mansion_gate_closed")
    RequestIpl("h4_se_ipl_02_slod")
    RequestIpl("h4_nw_ipl_02")
    RequestIpl("h4_ne_ipl_08_lod")
    RequestIpl("h4_sw_ipl_08")
    RequestIpl("h4_islandairstrip")
    RequestIpl("h4_islandairstrip_props_lod")
    RequestIpl("h4_se_ipl_05")
    RequestIpl("h4_ne_ipl_02_lod")
    RequestIpl("h4_islandx_maindock_props_2_lod")
    RequestIpl("h4_sw_ipl_03_slod")
    RequestIpl("h4_ne_ipl_01_slod")
    RequestIpl("h4_beach_props_slod")
    RequestIpl("h4_underwater_gate_closed")
    RequestIpl("h4_ne_ipl_00_lod")
    RequestIpl("h4_islandairstrip_doorsopen")
    RequestIpl("h4_sw_ipl_01_slod")
    RequestIpl("h4_se_ipl_00")
    RequestIpl("h4_se_ipl_06")
    RequestIpl("h4_islandx_mansion_lockup_02_lod")
    RequestIpl("h4_islandxtower_veg_lod")
    RequestIpl("h4_sw_ipl_00")
    RequestIpl("h4_se_ipl_04_lod")
    RequestIpl("h4_nw_ipl_07_slod")
    RequestIpl("h4_islandx_mansion_props_lod")
    RequestIpl("h4_islandairstrip_hangar_props")
    RequestIpl("h4_nw_ipl_06_lod")
    RequestIpl("h4_islandxtower_lod")
    RequestIpl("h4_islandxdock_lod")
    RequestIpl("h4_islandxdock_props_lod")
    RequestIpl("h4_beach_party")
    RequestIpl("h4_nw_ipl_06_slod")
    RequestIpl("h4_islandairstrip_doorsclosed")
    RequestIpl("h4_nw_ipl_00_lod")
    RequestIpl("h4_ne_ipl_02")
    RequestIpl("h4_islandxdock_slod")
    RequestIpl("h4_se_ipl_07_slod")
    RequestIpl("h4_islandxdock")
    RequestIpl("h4_islandxdock_props_2_slod")
    RequestIpl("h4_islandairstrip_props")
    RequestIpl("h4_sw_ipl_09")
    RequestIpl("h4_ne_ipl_06")
    RequestIpl("h4_se_ipl_03_lod")
    RequestIpl("h4_nw_ipl_03")
    RequestIpl("h4_islandx_mansion_lockup_01_lod")
    RequestIpl("h4_beach_lod")
    RequestIpl("h4_ne_ipl_07_lod")
    RequestIpl("h4_nw_ipl_01")
    RequestIpl("h4_mph4_island_lod")
    RequestIpl("h4_islandx_mansion_office_lod")
    RequestIpl("h4_islandairstrip_lod")
    RequestIpl("h4_beach_props_lod")
    RequestIpl("h4_nw_ipl_05_slod")
    RequestIpl("h4_islandx_checkpoint_lod")
    RequestIpl("h4_nw_ipl_05_lod")
    RequestIpl("h4_nw_ipl_03_slod")
    RequestIpl("h4_nw_ipl_03_lod")
    RequestIpl("h4_sw_ipl_05")
    RequestIpl("h4_mph4_mansion")
    RequestIpl("h4_sw_ipl_03")
    RequestIpl("h4_se_ipl_08_slod")
    RequestIpl("h4_mph4_island_ne_placement")
    RequestIpl("h4_aa_guns")
    RequestIpl("h4_islandairstrip_propsb_slod")
    RequestIpl("h4_sw_ipl_01")
    RequestIpl("h4_mansion_remains_cage")
    RequestIpl("h4_nw_ipl_01_slod")
    RequestIpl("h4_ne_ipl_06_lod")
    RequestIpl("h4_se_ipl_08")
    RequestIpl("h4_sw_ipl_04_slod")
    RequestIpl("h4_sw_ipl_04_lod")
    RequestIpl("h4_mph4_beach")
    RequestIpl("h4_sw_ipl_06_lod")
    RequestIpl("h4_sw_ipl_06_slod")
    RequestIpl("h4_se_ipl_00_lod")
    RequestIpl("h4_ne_ipl_07_slod")
    RequestIpl("h4_mph4_mansion_strm_0")
    RequestIpl("h4_nw_ipl_02_slod")
    RequestIpl("h4_mph4_airstrip")
    --RequestIpl("h4_mansion_gate_broken")
    RequestIpl("h4_island_padlock_props")
    RequestIpl("h4_islandairstrip_props_slod")
    RequestIpl("h4_nw_ipl_06")
    RequestIpl("h4_sw_ipl_09_lod")
    RequestIpl("h4_islandxcanal_props_lod")
    RequestIpl("h4_ne_ipl_05_slod")
    RequestIpl("h4_se_ipl_09_slod")
    RequestIpl("h4_islandx_mansion_vault_lod")
    RequestIpl("h4_se_ipl_03_slod")
    RequestIpl("h4_nw_ipl_08_lod")
    RequestIpl("h4_islandx_barrack_props_slod")
    RequestIpl("h4_islandxtower_veg_slod")
    RequestIpl("h4_sw_ipl_04")
    RequestIpl("h4_islandx_mansion_props")
    RequestIpl("h4_islandxtower_slod")
    RequestIpl("h4_beach_props")
    RequestIpl("h4_islandx_mansion_b_slod")
    RequestIpl("h4_islandx_maindock_props_slod")
    RequestIpl("h4_sw_ipl_07_slod")
    RequestIpl("h4_ne_ipl_07")
    RequestIpl("h4_islandxdock_props_2")
    RequestIpl("h4_ne_ipl_09_lod")
    RequestIpl("h4_islandxcanal_props")
    RequestIpl("h4_beach_slod")
    RequestIpl("h4_sw_ipl_00_slod")
    RequestIpl("h4_sw_ipl_03_lod")
    RequestIpl("h4_islandx_disc_strandedshark")
    RequestIpl("h4_islandx_disc_strandedshark_lod")
    RequestIpl("h4_islandx")
    RequestIpl("h4_islandx_props_lod")
    RequestIpl("h4_mph4_island_strm_0")
    RequestIpl("h4_islandx_sea_mines")
    RequestIpl("h4_mph4_island")
    RequestIpl("h4_boatblockers")
    RequestIpl("h4_mph4_island_long_0")
    RequestIpl("h4_islandx_disc_strandedwhale")
    RequestIpl("h4_islandx_disc_strandedwhale_lod")
    RequestIpl("h4_islandx_props")
    RequestIpl("h4_int_placement_h4_interior_1_dlc_int_02_h4_milo_")
    RequestIpl("h4_int_placement_h4_interior_0_int_sub_h4_milo_")
    RequestIpl("h4_int_placement_h4")

end)

-- Casino

Citizen.CreateThread(function()
	RequestIpl("vw_casino_main")
	RequestIpl("vw_casino_penthouse")
	RequestIpl("hei_dlc_casino_door")
	RequestIpl("vw_dlc_casino_door")
	RequestIpl("hei_dlc_windows_casino")
	SetStaticEmitterEnabled('COUNTRYSIDE_ALTRUIST_CULT_01', false)
    SetStaticEmitterEnabled('LOS_SANTOS_AMMUNATION_GUN_RANGE', false)
end)

-- Nightclub

Citizen.CreateThread(function()
	RequestIpl("ba_int_placement_ba_interior_0_dlc_int_01_ba_milo_")
	local interiorID = GetInteriorAtCoords(376.91300000, 275.42500000, 90.19600000)

	if IsValidInterior(interiorID) then
		-- CLUB NAME
		EnableInteriorProp(interiorID, "int01_ba_clubname_01")
		-- CLUB STYLE
		EnableInteriorProp(interiorID, "int01_ba_style03")
		-- PODIUM TYPE
		EnableInteriorProp(interiorID, "int01_ba_style03_podium")
		-- SPEAKERS
		EnableInteriorProp(interiorID, "int01_ba_equipment_setup", "int01_ba_equipment_upgrade")
		-- SECURITY
		EnableInteriorProp(interiorID, "int01_ba_security_upgrade")
		-- DJ SETUP
		EnableInteriorProp(interiorID, "int01_ba_dj03")
		-- LIGHTING SYSTEM
		-- FLOOR LIGHTS
		EnableInteriorProp(interiorID, "int01_ba_lightgrid_01")
		-- LIGHT SCREEN
		EnableInteriorProp(interiorID, "int01_ba_lights_screen")
		-- SCREEN
		-- EnableInteriorProp(interiorID, "int01_ba_screen")
		-- DROPLETS
		-- EnableInteriorProp(interiorID, "dj_03_lights_01")
		-- NEONS
		EnableInteriorProp(interiorID, "dj_03_lights_02")
		-- BANDS
		EnableInteriorProp(interiorID, "dj_03_lights_03")
		-- LASERS
		-- EnableInteriorProp(interiorID, "dj_01_lights_01")
		-- BAR
		EnableInteriorProp(interiorID, "int01_ba_bar_content")
		-- BOOZE
		EnableInteriorProp(interiorID, "int01_ba_booze_01")
		EnableInteriorProp(interiorID, "int01_ba_booze_02")
		EnableInteriorProp(interiorID, "int01_ba_booze_03")
		-- TROPHY
		EnableInteriorProp(interiorID, "int01_ba_trophy03")
		EnableInteriorProp(interiorID, "int01_ba_trophy04")
		SetInteriorPropColor(interiorID, "int01_ba_trophy03", 2)
		-- CLUTTER
		-- EnableInteriorProp(interiorID, "int01_ba_clutter")

		RefreshInterior(interiorID)

	end	
end)

-- -- Arcade Main Interior
	
-- Citizen.CreateThread(function()
-- 	RequestIpl("ch_int_placement_ch_interior_1_dlc_arcade_milo_")
-- 	local interiorID = GetInteriorAtCoords(2730.000, -380.000, -50.000)

-- 	if IsValidInterior(interiorID) then
-- 		-- MAIN STYLES (Choose one)
-- 		EnableInteriorProp(interiorID, "casino_arcade_style_01") 
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_style_02")
		
-- 		-- DESTROYED VERSION
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_destroyed")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_destroyed_extras") -- (extras for the destroyed version)
		
-- 		-- TEXTURE STYLES (Choose one)
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_01") 
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_02") 
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_03") 
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_04") 
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_09")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_10")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_11")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_texture_style_16")
		
-- 		-- WALL DESIGNS (Choose one)
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_01")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_02")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_03")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_04")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_05")
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_06")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_07")
-- 		-- EnableInteriorProp(interiorID, "casino_arcade_extraprops_wall_08")
			
-- 		-- PROPS: Can all be used at same time without colliding
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_streetgames_01")
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_wallmonitors")
-- 		EnableInteriorProp(interiorID, "casino_arcade_no_idea") -- Some floor stuff
-- 		EnableInteriorProp(interiorID, "casino_arcade_no_idea2") -- Neon stuff i think
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_barstuff")
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_walltv")
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_lights_01") -- This also has trophies etc
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_lights_02")
-- 		EnableInteriorProp(interiorID, "casino_arcade_extraprops_wire") -- This has extra added arcade game props
		
-- 		RefreshInterior(interiorID)

-- 	end	
-- end)

-- -- Arcade Planing Garage

-- Citizen.CreateThread(function()
-- 	RequestIpl("ch_int_placement_ch_interior_2_dlc_plan_milo_")
-- 	local interiorID = GetInteriorAtCoords(2697.615, -376.3892, -56.46193)

-- 	if IsValidInterior(interiorID) then
-- 		-- PROPS: Can all be used at same time without colliding
-- 	EnableInteriorProp(interiorID, "casino_plan_hacking")
-- 	EnableInteriorProp(interiorID, "casino_plan_keypads")
-- 	EnableInteriorProp(interiorID, "casino_plan_hacking2")
-- 	EnableInteriorProp(interiorID, "casino_plan_work")
-- 	EnableInteriorProp(interiorID, "casino_plan_work2")
-- 	EnableInteriorProp(interiorID, "casino_plan_vaultplan")
-- 	EnableInteriorProp(interiorID, "casino_plan_work3")
-- 	EnableInteriorProp(interiorID, "casino_plan_casino_tablemodel") -- Has to be used together with: casino_plan_work3 (its on a table)
-- 	EnableInteriorProp(interiorID, "casino_plan_work4")
-- 	EnableInteriorProp(interiorID, "casino_plan_work5")
-- 	EnableInteriorProp(interiorID, "casino_plan_board_drawing")
-- 	EnableInteriorProp(interiorID, "casino_plan_machines")
-- 	EnableInteriorProp(interiorID, "casino_plan_blueprints")
-- 	EnableInteriorProp(interiorID, "casino_plan_c4")
-- 	EnableInteriorProp(interiorID, "casino_plan_insect")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_01")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_02")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_03")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_04")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_05")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_hat")
-- 	EnableInteriorProp(interiorID, "casino_plan_drone")
-- 	EnableInteriorProp(interiorID, "casino_plan_noidea_xd")
-- 	EnableInteriorProp(interiorID, "casino_plan_equipment_07")

-- 	RefreshInterior(interiorID)
	
-- 	end	
-- end)

-- Biker Clubhouse (Lost MC)

Citizen.CreateThread(function()
	RequestIpl("gabz_biker_milo_")
	local interiorID = GetInteriorAtCoords(994.47870000, -122.99490000, 73.11467000)
		
	if IsValidInterior(interiorID) then
		EnableInteriorProp(interiorID, "walls_02")
		SetInteriorPropColor(interiorID, "walls_02", 8)
		EnableInteriorProp(interiorID, "Furnishings_02")
		SetInteriorPropColor(interiorID, "Furnishings_02", 8)
		EnableInteriorProp(interiorID, "decorative_02")
		EnableInteriorProp(interiorID, "mural_03")
		EnableInteriorProp(interiorID, "lower_walls_default")
		SetInteriorPropColor(interiorID, "lower_walls_default", 8)
		EnableInteriorProp(interiorID, "mod_booth")
		EnableInteriorProp(interiorID, "gun_locker")
		EnableInteriorProp(interiorID, "cash_small")
		EnableInteriorProp(interiorID, "id_small")
		EnableInteriorProp(interiorID, "weed_small")
	
		RefreshInterior(interiorID)
	
	end	
end)

-- Prison

Citizen.CreateThread(function()
	RequestIpl("bobo_prison_milo_")
	local interiorID = GetInteriorAtCoords(1756.861, 2486.683, 48.37542) 
	
	if IsValidInterior(interiorID) then	
		ActivateInteriorEntitySet(interiorID, "shell")
		ActivateInteriorEntitySet(interiorID, "bobo_prison_shell")
		
		RefreshInterior(interiorID)
	end
end)

--[[

--------------------------------------------
Customization of each yatch explained:
--------------------------------------------

In the stream folder you can find each ipl ymap per yacht, if you want to change the color on
a yacht you can open the ymap either in OpenIV or Codewalker and change the "tint Value" on the items named:
"apa_mp_apa_yacht" and "apa_mp_apa_yacht_option3"

Color list:

Pacific = 0
Azure = 1  
Nautical = 2  
Continental = 3 
Battleship = 4
Intrepid = 5
Uniform = 6
Classico = 7 
Mediterranean = 8
Command = 9 
Mariner = 10
Ruby = 11
Vintage = 12
Pristine = 13 
Merchant = 14  
Voyager = 15  

If you want to change the colors of the light on the yacht you can change the itemname on the prop named:
"apa_mp_apa_y3_l2a" to one of the following:

apa_mp_apa_y3_l2a - Yellow: https://i.gyazo.com/c81501c9539154e5bcafaeb8c974d5ea.png
apa_mp_apa_y3_l2b - Blue: https://i.gyazo.com/887dec63b0f87eedfe8aee2c952272af.png
apa_mp_apa_y3_l2c - Pink: https://i.gyazo.com/50a420260ed97eca01d2fff5acba2b8e.jpg
apa_mp_apa_y3_l2d - Green: https://i.gyazo.com/6dcf44f7548c6ff6b7a32b045eb9760d.png

If you dont want to have all yachts loaded you can simple turn enabled to false in the table below.

]] 

local yachts = {
	{ enabled = true, coords = { x = -3542.82200000, y = 1488.25000000, z = 5.42990900 }, ipls = { "apa_yacht_grp01_1", "apa_yacht_grp01_1_int", "apa_yacht_grp01_1_lod" }, },
	{ enabled = true, coords = { x = -3148.37900000, y = 2807.55500000, z = 5.43004400 }, ipls = { "apa_yacht_grp01_2", "apa_yacht_grp01_2_int", "apa_yacht_grp01_2_lod" }, },
	{ enabled = true, coords = { x = -3280.50100000, y = 2140.50700000, z = 5.42995500 }, ipls = { "apa_yacht_grp01_3", "apa_yacht_grp01_3_int", "apa_yacht_grp01_3_lod" }, },
	{ enabled = true, coords = { x = -2814.48900000, y = 4072.74000000, z = 5.42835300 }, ipls = { "apa_yacht_grp02_1", "apa_yacht_grp02_1_int", "apa_yacht_grp02_1_lod" }, },
	{ enabled = true, coords = { x = -3254.55200000, y = 3685.67600000, z = 5.42995500 }, ipls = { "apa_yacht_grp02_2", "apa_yacht_grp02_2_int", "apa_yacht_grp02_2_lod" }, },
	{ enabled = true, coords = { x = -2368.44100000, y = 4697.87400000, z = 5.42995500 }, ipls = { "apa_yacht_grp02_3", "apa_yacht_grp02_3_int", "apa_yacht_grp02_3_lod" }, },
	{ enabled = true, coords = { x = -3205.34400000, y = -219.01040000, z = 5.42995500 }, ipls = { "apa_yacht_grp03_1", "apa_yacht_grp03_1_int", "apa_yacht_grp03_1_lod" }, },
	{ enabled = true, coords = { x = -3448.25400000, y = 311.50180000, z = 5.42995500 }, ipls = { "apa_yacht_grp03_2", "apa_yacht_grp03_2_int", "apa_yacht_grp03_2_lod" }, },
	{ enabled = true, coords = { x = -2697.86200000, y = -540.61230000, z = 5.42995500 }, ipls = { "apa_yacht_grp03_3", "apa_yacht_grp03_3_int", "apa_yacht_grp03_3_lod" }, },
	{ enabled = true, coords = { x = -1995.72500000, y = -1523.69400000, z = 5.42997000 }, ipls = { "apa_yacht_grp04_1", "apa_yacht_grp04_1_int", "apa_yacht_grp04_1_lod" }, },
	{ enabled = true, coords = { x = -2117.58100000, y = -2543.34600000, z = 5.42995500 }, ipls = { "apa_yacht_grp04_2", "apa_yacht_grp04_2_int", "apa_yacht_grp04_2_lod" }, },
	{ enabled = true, coords = { x = -1605.07400000, y = -1872.46800000, z = 5.42995500 }, ipls = { "apa_yacht_grp04_3", "apa_yacht_grp04_3_int", "apa_yacht_grp04_3_lod" }, },
	{ enabled = true, coords = { x = -753.08170000, y = -3919.06800000, z = 5.42995500 }, ipls = { "apa_yacht_grp05_1", "apa_yacht_grp05_1_int", "apa_yacht_grp05_1_lod" }, },
	{ enabled = true, coords = { x = -351.06080000, y = -3553.32300000, z = 5.42995500 }, ipls = { "apa_yacht_grp05_2", "apa_yacht_grp05_2_int", "apa_yacht_grp05_2_lod" }, },
	{ enabled = true, coords = { x = -1460.53600000, y = -3761.46700000, z = 5.42995500 }, ipls = { "apa_yacht_grp05_3", "apa_yacht_grp05_3_int", "apa_yacht_grp05_3_lod" }, },
	{ enabled = true, coords = { x = 1546.89200000, y = -3045.62700000, z = 5.43018400 }, ipls = { "apa_yacht_grp06_1", "apa_yacht_grp06_1_int", "apa_yacht_grp06_1_lod" }, },
	{ enabled = true, coords = { x = 2490.88600000, y = -2428.84800000, z = 5.42995500 }, ipls = { "apa_yacht_grp06_2", "apa_yacht_grp06_2_int", "apa_yacht_grp06_2_lod" }, },
	{ enabled = true, coords = { x = 2049.79000000, y = -2821.62400000, z = 5.42995500 }, ipls = { "apa_yacht_grp06_3", "apa_yacht_grp06_3_int", "apa_yacht_grp06_3_lod" }, },
	{ enabled = true, coords = { x = 3029.01800000, y = -1495.70200000, z = 5.42996800 }, ipls = { "apa_yacht_grp07_1", "apa_yacht_grp07_1_int", "apa_yacht_grp07_1_lod" }, },
	{ enabled = true, coords = { x = 3021.25400000, y = -723.39030000, z = 5.42998600 }, ipls = { "apa_yacht_grp07_2", "apa_yacht_grp07_2_int", "apa_yacht_grp07_2_lod" }, },
	{ enabled = true, coords = { x = 2976.62200000, y = -1994.76000000, z = 5.42995500 }, ipls = { "apa_yacht_grp07_3", "apa_yacht_grp07_3_int", "apa_yacht_grp07_3_lod" }, },
	{ enabled = true, coords = { x = 3404.51000000, y = 1977.04400000, z = 5.42995500 }, ipls = { "apa_yacht_grp08_1", "apa_yacht_grp08_1_int", "apa_yacht_grp08_1_lod" }, },
	{ enabled = true, coords = { x = 3411.10000000, y = 1193.44500000, z = 5.43006200 }, ipls = { "apa_yacht_grp08_2", "apa_yacht_grp08_2_int", "apa_yacht_grp08_2_lod" }, },
	{ enabled = true, coords = { x = 3784.80200000, y = 2548.54100000, z = 5.42995500 }, ipls = { "apa_yacht_grp08_3", "apa_yacht_grp08_3_int", "apa_yacht_grp08_3_lod" }, },
	{ enabled = true, coords = { x = 4225.02800000, y = 3988.00200000, z = 5.42995500 }, ipls = { "apa_yacht_grp09_1", "apa_yacht_grp09_1_int", "apa_yacht_grp09_1_lod" }, },
	{ enabled = true, coords = { x = 4250.58100000, y = 4576.56500000, z = 5.42995500 }, ipls = { "apa_yacht_grp09_2", "apa_yacht_grp09_2_int", "apa_yacht_grp09_2_lod" }, },
	{ enabled = true, coords = { x = 4204.35600000, y = 3373.70000000, z = 5.42995500 }, ipls = { "apa_yacht_grp09_3", "apa_yacht_grp09_3_int", "apa_yacht_grp09_3_lod" }, },
	{ enabled = true, coords = { x = 3751.68100000, y = 5753.50100000, z = 5.42995500 }, ipls = { "apa_yacht_grp10_1", "apa_yacht_grp10_1_int", "apa_yacht_grp10_1_lod" }, },
	{ enabled = true, coords = { x = 3490.10500000, y = 6305.78500000, z = 5.42995500 }, ipls = { "apa_yacht_grp10_2", "apa_yacht_grp10_2_int", "apa_yacht_grp10_2_lod" }, },
	{ enabled = true, coords = { x = 3684.85300000, y = 5212.23800000, z = 5.42995500 }, ipls = { "apa_yacht_grp10_3", "apa_yacht_grp10_3_int", "apa_yacht_grp10_3_lod" }, },
	{ enabled = true, coords = { x = 581.59550000, y = 7124.55800000, z = 5.42995500 }, ipls = { "apa_yacht_grp11_1", "apa_yacht_grp11_1_int", "apa_yacht_grp11_1_lod" }, },
	{ enabled = true, coords = { x = 2004.46200000, y = 6907.15700000, z = 5.42997400 }, ipls = { "apa_yacht_grp11_2", "apa_yacht_grp11_2_int", "apa_yacht_grp11_2_lod" }, },
	{ enabled = true, coords = { x = 1396.63800000, y = 6860.20300000, z = 5.42995900 }, ipls = { "apa_yacht_grp11_3", "apa_yacht_grp11_3_int", "apa_yacht_grp11_3_lod" }, },
	{ enabled = true, coords = { x = -1170.69000000, y = 5980.68100000, z = 5.42994400 }, ipls = { "apa_yacht_grp12_1", "apa_yacht_grp12_1_int", "apa_yacht_grp12_1_lod" }, },
	{ enabled = false, coords = { x = -777.48650000, y = 6566.90700000, z = 5.42995500 }, ipls = { "apa_yacht_grp12_2", "apa_yacht_grp12_2_int", "apa_yacht_grp12_2_lod" }, },
	{ enabled = false, coords = { x = -381.77390000, y = 6946.96000000, z = 5.42990000 }, ipls = { "apa_yacht_grp12_3", "apa_yacht_grp12_3_int", "apa_yacht_grp12_3_lod" }, },
}

CreateThread(function()
	RequestIpl("apa_ch2_superyacht")
    for _, yacht in ipairs(yachts) do
        if yacht.enabled then
            for __, ipl in ipairs(yacht.ipls) do
                RequestIpl(ipl)
            end
        end
    end    
end)


