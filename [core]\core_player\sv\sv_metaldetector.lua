local metalDetectors = {
    vector3(427.4135, -982.0465, 30.7103),
    vector3(1853.30, 3688.76, 34.27),
    vector3(-447.67, 6012.87, 31.71)
}

RegisterNetEvent("world:alarmMetalDetector", function(objCoords, hasWeapons)
    TriggerClientEvent("world:alarmMetalDetector", -1, objCoords, hasWeapons)
end)

RegisterNetEvent("world:checkMetalDetectors", function()
    local src = source
    local playerPed = GetPlayerPed(src)
    local playerCoords = GetEntityCoords(playerPed)

    for _, coords in pairs(metalDetectors) do
        if #(playerCoords - coords) < 0.5 then
            local hasWeapons = exports.ox_inventory:Search(src, "count", "weapon") > 0
            TriggerClientEvent("world:alarmMetalDetector", src, coords, hasWeapons)
            break
        end
    end
end)
