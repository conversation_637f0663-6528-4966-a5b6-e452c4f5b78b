local QBCore = exports['qb-core']:GetCoreObject()
local Webhooks = {
    ['default'] = '',
    ['testwebhook'] = '',
    ['playermoney'] = '',
    ['playerinventory'] = '',
    ['robbing'] = 'https://canary.discord.com/api/webhooks/1268787463061508126/BUieBYnSDBX4GFmtzWnYBpqfFFobJlxn9qqVHtnJwOlgE0KJQbWus-sLtNE09UmsfAmP',
    ['cuffing'] = '',
    ['drop'] = '',
    ['trunk'] = '',
    ['stash'] = '',
    ['glovebox'] = '',
    ['banking'] = '',
    ['vehicleshop'] = '',
    ['vehicleupgrades'] = '',
    ['shops'] = '',
    ['adminactions'] = 'https://canary.discord.com/api/webhooks/1281463730378510418/dFYXMhMr87MTUtlBt2M6dhJRe6rDVgfm5pPskiJUylp4Dp43GgbNvM8Z9kEaUnARpwPJ',
    ['dealers'] = '',
    ['storerobbery'] = '',
    ['bankrobbery'] = '',
    ['craftingexploit'] = '',
    ['powerplants'] = '',
    ['hwid'] = 'https://canary.discord.com/api/webhooks/1279263979620929668/vBS72nYGk73ZQylw47fvpP6NPPP0SGWc1AzAZ-c0FwALhthWxFsIuOi3ihUbAlLc3fvN',
    ['plateswap'] = 'https://canary.discord.com/api/webhooks/1271859543776956552/z3Ap7KkWvmtO0mrRdKH44P3DDuaIjwkP4xyuTm1U4xc329lSX91VNnfeq459oO3azVT0',
    ['hostage'] = 'https://canary.discord.com/api/webhooks/1272463442493575198/DjmqELJmgiXisExR3FIddjNvDYNlDNJbG-_3Njz_MPLuVFlSsbYbkmkBrX3nh9LDqkxD',
    ['playerdisconnected'] = 'https://canary.discord.com/api/webhooks/1272204976533409812/u5UcGh_ek2ZgjXmb8l4GAXGRn1ZEAAOxvbKZXTAkhYfqvRxVtTUQCN7NwdIjyMdsInWn',
    ['treasurebox'] = 'https://canary.discord.com/api/webhooks/1272200297930031168/uYOEECmBJnfPaWnvxeqDLZ9fAtd6R-nYLXmgcM729cTjEbNixChYTV_kSZmYeughg1DH',
    ['pedmenuradius'] = 'https://canary.discord.com/api/webhooks/1272199211244585051/OtSozw7jtPA1tT2TFPcA1Fz3dkYMeQ1SPFF7MnVfdSwwYdtr27JFVXIxgqsu5_dgEB2y',
    ['reviveradius'] = 'https://canary.discord.com/api/webhooks/1272198921162068049/NOeWHKQ1e-81W2QRadEWqX8bvsJYatkQIhSU1JXcmfnqqPuJ86ptIY6Al_F6f5F22fM3',
    ['death'] = 'https://canary.discord.com/api/webhooks/1268768061138276433/JBCxKIQNDuIMzr1kkMJjp2y4YRA6AXUEiHYPurI-vprTA2xsOWncJvh6ftPzM7CwN2_t',
    ['joinleave'] = 'https://canary.discord.com/api/webhooks/1268767745483476992/q8En5iqjt3DzYE5pvVPrgiM_0B8afmddtvnTQHDu3GvVz5t3a8fgQrw9OkWS2LlTO2Yk',
    ['ooc'] = '',
    ['report'] = '',
    ['me'] = '',
    ['pmelding'] = '',
    ['112'] = '',
    ['metaldetectingexploit'] = 'https://canary.discord.com/api/webhooks/1270585557864419359/Z2W55qETkERlZiMD7GVlFEZTLP6VnuU2EfGwwxzDWxPuBYaxIqW_ny4B5Iby7mgT15xf',
    ['bans'] = '',
    ['anticheat'] = 'https://canary.discord.com/api/webhooks/1263085681413066893/GaNGvmtT1j7Udvyrc1e_akaz3hcV62_8PHBXHv5194aFeEAbd7Aztcyh--74M6gNF3RR',
    ['weather'] = '',
    ['moneysafes'] = '',
    ['bennys'] = '',
    ['bossmenu'] = 'https://canary.discord.com/api/webhooks/1264896563033931797/ws0XWlfkxuf_1pnuGtm9RCreXW6iQhSOD_luDRI-OCpCs2zXlM9CgZKJo7A9_X4OVFNM',
    ['robbery'] = '',
    ['casino'] = '',
    ['traphouse'] = '',
    ['911'] = '',
    ['palert'] = '',
    ['house'] = '',
    ['qbjobs'] = '',
    ['cayo'] = 'https://canary.discord.com/api/webhooks/1269879556161011763/FSgZxdQEPFgjnaeIHb1j2oW4Fc3zkzdvWuWFrclsoAbz8gES60xy73SFzDn-lF_6OY1b',
    ['miningexploit'] = 'https://canary.discord.com/api/webhooks/1269595674819694694/4bVy3X_ReSS-SmVa4DF0_0OjXcdNu2OblnoC-3SBDfTF-n-A04DSfw9vChJl26cLqI9c',
    ['combatlog'] = 'https://canary.discord.com/api/webhooks/1268784826584924232/O4Mza0O_lJc4q8WBFlQ5nID7-VSZGD0tHsZmADhZXQA_tNvTnVmld3yVm6tGIXZl0JOT',
}

local colors = { -- https://www.spycolor.com/
    ['default'] = 14423100,
    ['blue'] = 255,
    ['red'] = 16711680,
    ['green'] = 65280,
    ['white'] = 16777215,
    ['black'] = 0,
    ['orange'] = 16744192,
    ['yellow'] = 16776960,
    ['pink'] = 16761035,
    ['lightgreen'] = 65309,
}

local logQueue = {}

RegisterNetEvent('qb-log:server:CreateLog', function(name, title, color, message, tagEveryone)
    local postData = {}
    local tag = tagEveryone or false
    if not Webhooks[name] then print('Tried to call a log that isn\'t configured with the name of ' ..name) return end
    local webHook = Webhooks[name] ~= '' and Webhooks[name] or Webhooks['default']
    local embedData = {
        {
            ['title'] = title,
            ['color'] = colors[color] or colors['default'],
            ['footer'] = {
                ['text'] = os.date('%c'),
            },
            ['description'] = message,
            ['author'] = {
                ['name'] = 'QBCore Logs',
                ['icon_url'] = 'https://raw.githubusercontent.com/GhzGarage/qb-media-kit/main/Display%20Pictures/Logo%20-%20Display%20Picture%20-%20Stylized%20-%20Red.png',
            },
        }
    }

    if not logQueue[name] then logQueue[name] = {} end
    logQueue[name][#logQueue[name] + 1] = {webhook = webHook, data = embedData}

    if #logQueue[name] >= 10 then
        if tag then
            postData = {username = 'QB Logs', content = '@everyone', embeds = {}}
        else
            postData = {username = 'QB Logs', embeds = {}}
        end
        for i = 1, #logQueue[name] do postData.embeds[#postData.embeds + 1] = logQueue[name][i].data[1] end
        PerformHttpRequest(logQueue[name][1].webhook, function() end, 'POST', json.encode(postData), { ['Content-Type'] = 'application/json' })
        logQueue[name] = {}
    end
end)

Citizen.CreateThread(function()
    local timer = 0
    while true do
        Wait(1000)
        timer = timer + 1
        if timer >= 60 then -- If 60 seconds have passed, post the logs
            timer = 0
            for name, queue in pairs(logQueue) do
                if #queue > 0 then
                    local postData = {username = 'QB Logs', embeds = {}}
                    for i = 1, #queue do
                        postData.embeds[#postData.embeds + 1] = queue[i].data[1]
                    end
                    PerformHttpRequest(queue[1].webhook, function() end, 'POST', json.encode(postData), {['Content-Type'] = 'application/json'})
                    logQueue[name] = {}
                end
            end
        end
    end
end)

QBCore.Commands.Add('testwebhook', 'Test Your Discord Webhook For Logs (God Only)', {}, false, function()
    TriggerEvent('qb-log:server:CreateLog', 'testwebhook', 'Test Webhook', 'default', 'Webhook setup successfully')
end, 'god')