local function canUseStormram(action)
    local ClosestDoor = exports.ox_doorlock:getClosestDoor()
    
    if not ClosestDoor then
        return false
    end
    
    if action == 'useStormram' then
        return ClosestDoor.state == 1
    elseif action == 'closeDoor' then
        return ClosestDoor.state == 0
    end
    
    return false
end

CreateThread(function()
    exports.ox_target:addGlobalObject({
        {
            name = 'useStormram',
            label = 'Use Lockpick',
            icon = 'fas fa-user-lock',
            canInteract = function() 
                local ClosestDoor = exports.ox_doorlock:getClosestDoor()
                return ClosestDoor and canUseStormram('useStormram') and ClosestDoor.distance <= 2
            end,
            event = 'main_stormram:client:useStormram',
            items = 'advlockpick',
            anyItem = true,
            distance = 1
        },
        {
            name = 'closeDoor',
            label = 'Lock door',
            icon = 'fas fa-user-lock',
            canInteract = function() 
                local ClosestDoor = exports.ox_doorlock:getClosestDoor()
                return ClosestDoor and canUseStormram('closeDoor') and ClosestDoor.distance <= 2
            end,
            event = 'main_stormram:client:useStormram',
            items = 'advlockpick',
            anyItem = true,
            distance = 1
        }
    })
end)

RegisterNetEvent('main_stormram:client:useStormram', function(source)
    local PlayerData = exports.qbx_core:GetPlayerData()
    local ClosestDoor = exports.ox_doorlock:getClosestDoor()

    if ClosestDoor.distance > 2 then 
        lib.notify({
            title = 'Lockpicking Kit',
            description = 'There are no doors near you!',
            type = 'error',
            icon = 'door-closed',
            iconColor = '#FF5733'
        })
        return
    end
    
    local coords = ClosestDoor.coords
    local entity = ClosestDoor.entity
    local playerPed = PlayerPedId()

    TaskTurnPedToFaceCoord(playerPed, coords.x, coords.y, coords.z, 2000)
    Wait(500)

    if ClosestDoor.state == 0 then 
        if lib.progressBar({
            duration = 4000,
            position = 'bottom',
            label = 'Locking door...',
            useWhileDead = false,
            canCancel = true,
            disable = {
                move = true,
                car = true,
                combat = true,
            },
            anim = {
                scenario = 'PROP_HUMAN_PARKING_METER',
            },
        })
        then    
            TriggerServerEvent('main_stormram:server:setState', ClosestDoor.id, 1) 
        else 
            lib.notify({
                title = 'Lockpicking Kit',
                description = 'Cancelled!',
                type = 'error',
                icon = 'times-circle',
            })
        end
    else
        if lib.progressBar({
            duration = 4000,
            position = 'bottom',
            label = 'Lockpicking Door...',
            useWhileDead = false,
            canCancel = true,
            disable = {
                move = true,
                car = true,
                combat = true,
            },
            anim = {
                dict = 'missheistfbi3b_ig7',
                clip = 'lift_fibagent_loop'
            },
        })
        then 
            local randomChance = math.random(1, 100)

            if randomChance <= 50 then
                TriggerServerEvent('main_stormram:server:setState', ClosestDoor.id, 0)
                lib.notify({
                    title = 'Lockpicking Kit',
                    description = 'You opened the door!',
                    type = 'success',
                    icon = 'unlock',
                })
            else
                lib.notify({
                    title = 'Lockpicking Kit',
                    description = 'You couldn\'t do it! Try Again',
                    type = 'error',
                    icon = 'times-circle',
                })
            end
        else 
            lib.notify({
                title = 'Lockpicking Kit',
                description = 'Cancelled!',
                type = 'error',
                icon = 'times-circle',
            })
        end    
    end
end)