return {
    robDistance =  3.0,
    robCooldown = 1, -- Cooldown in minutes
    robDuration = 5,  -- Seconds to break into a property (progress bar)
    searchDuration = 10, -- Seconds to search a searchPoint in the property.
    craftDuration = 5, -- Seconds to craft lockpicks 
    craftBreakChance = 30, -- Chance for lockpicks to break? (30 = 30%)

    properties = {
        ['house'] = {
            ['small'] = { 
                coords = vec4(172.3886, -1008.4391, -100.0000, 4.9327), -- Enter/Exit at this point
                peds = {
                    owner = 'u_m_y_smugmech_01',
                    ownerCoords = vec4(171.5567, -1000.1029, -99.9999, 194.9296),
                    ownerSpawnChance = 20, -- Chance owner will spawn (20 = 20%)
                    dog = 'a_c_chop',
                    dogCoords = vec4(172.0175, -1000.5131, -99.9999, 206.3966),
                    dogSpawnChance = 20, -- Chance dog will spawn (50 = 50%) 
                },
                searchPoints = {
                    { -- Back wall desk
                        coords = vec3(169.3177, -1005.2059, -100.0000),
                        loot = {
                            {item = 'water', amount = math.random(1, 3), chance = 100}
                        },
                        customText = "[~b~G~w~] Search Back Wall Desk"
                    },
                    { -- Safe
                        coords = vec3(176.6030, -1001.4230, -100.000),
                        loot = {
                            {item = 'money', amount = math.random(1, 3), chance = 100}
                        },
                        minigame = 'safe',
                        model = `p_v_43_safe_s`,
                        modelCoords = vec4(176.66329956055, -1000.7288208008, -99.249923706055, 160.5298),
                        customText = "[~b~G~w~] Crack"
                    },
                    { -- Left wall shelf
                        coords = vec3(173.8447, -1000.0678, -100.0000),
                        loot = {
                            {item = 'steel', amount = math.random(1, 3), chance = 100},
                            {item = 'stevo_policebadge', amount = 1, chance = 100}
                        }
                    }
                },
            }
        },
        ['garage'] = {
            ['small'] = {
                coords = vec4(172.3886, -1008.4391, -100.0000, 4.9327), -- Enter/Exit at this point
                peds = {
                    owner = 'u_m_y_smugmech_01',
                    ownerCoords = vec4(171.5567, -1000.1029, -99.9999, 194.9296),
                    dog = 'a_c_chop',
                    dogCoords = vec4(172.0175, -1000.5131, -99.9999, 206.3966),
                    dogSpawnChance = 20,
                },
                searchPoints = {
                    {
                        coords = vec3(169.25, -1002.57, -100.0),
                        loot = {
                            -- Low Tier Items (High Chance)
                            {item = 'bobbypins', amount = math.random(1, 3), chance = 70},
                            {item = 'plastic_bottle', amount = math.random(1, 3), chance = 60},
                            {item = 'scrap_metal', amount = math.random(1, 3), chance = 80},
                            {item = 'rubber', amount = math.random(1, 3), chance = 50},
                            {item = 'cloth_pack', amount = math.random(1, 3), chance = 70},
                            -- Mid Tier Items (Lower Chance)
                            {item = 'casio', amount = 1, chance = 10},
                            {item = 'necklace', amount = 1, chance = 15},
                        },
                        customText = "[~b~G~w~] Search Shelves"
                    },
                    { 
                        coords = vec3(171.07, -1000.12, -100.0),
                        loot = {
                            -- Low Tier Items (High Chance)
                            {item = 'cheapnokia', amount = 1, chance = 50},
                            {item = 'old_nokia', amount = 1, chance = 40},
                            {item = 'nokia', amount = 1, chance = 30},
                            {item = 'tvremote', amount = 1, chance = 60},
                            {item = 'shaver', amount = 1, chance = 50},
                            -- Mid Tier Items (Lower Chance)
                            {item = 'dicontewatch', amount = 1, chance = 10},
                            {item = 'ring_98', amount = 1, chance = 15},
                        },
                        customText = "[~b~G~w~] Search Shelves"
                    },
                    { 
                        coords = vec3(174.57, -1000.12, -100.0),
                        loot = {
                            -- Low Tier Items (High Chance)
                            {item = 'bobbypins', amount = math.random(1, 3), chance = 70},
                            {item = 'plastic_bottle', amount = math.random(1, 3), chance = 60},
                            {item = 'scrap_metal', amount = math.random(1, 3), chance = 80},
                            {item = 'rubber', amount = math.random(1, 3), chance = 50},
                            {item = 'cloth_pack', amount = math.random(1, 3), chance = 70},
                            -- Mid Tier Items (Lower Chance)
                            {item = 'gold_coin', amount = 1, chance = 10},
                            {item = 'gold_necklace', amount = 1, chance = 15},
                        },
                        customText = "[~b~G~w~] Search Shelves"
                    },
                    { 
                        coords = vec3(177.1, -1002.52, -100.0),
                        loot = {
                            -- Low Tier Items (High Chance)
                            {item = 'cheapnokia', amount = 1, chance = 50},
                            {item = 'old_nokia', amount = 1, chance = 40},
                            {item = 'nokia', amount = 1, chance = 30},
                            {item = 'tvremote', amount = 1, chance = 60},
                            {item = 'shaver', amount = 1, chance = 50},
                            -- High Tier Items (Lower Chance)
                            {item = 'rolex', amount = 1, chance = 5},
                            {item = 'diamond', amount = 1, chance = 10},
                        },
                        customText = "[~b~G~w~] Search Shelves"
                    },
                    { 
                        coords = vec3(178.59, -1007.68, -100.0),
                        loot = {
                            -- Low Tier Items (High Chance)
                            {item = 'bobbypins', amount = math.random(1, 3), chance = 70},
                            {item = 'plastic_bottle', amount = math.random(1, 3), chance = 60},
                            {item = 'scrap_metal', amount = math.random(1, 3), chance = 80},
                            {item = 'rubber', amount = math.random(1, 3), chance = 50},
                            {item = 'cloth_pack', amount = math.random(1, 3), chance = 70},
                            -- High Tier Items (Lower Chance)
                            {item = 'emerald', amount = 1, chance = 10},
                            {item = 'ruby', amount = 1, chance = 10},
                        },
                        customText = "[~b~G~w~] Search Shelves"
                    },
                    { -- Safe
                        coords = vec3(181.28, -1001.21, -100.1),
                        loot = {
                            -- Safe Tier Items (No Low Tier Items)
                            {item = 'bitcoin', amount = 1, chance = 5},
                            {item = 'gold', amount = math.random(1, 3), chance = 20},
                            {item = 'silver', amount = math.random(1, 3), chance = 20},
                            {item = 'vintage_coins', amount = 1, chance = 15},
                            {item = 'valuablegoods', amount = 1, chance = 10},
                        },
                        minigame = 'safe',
                        model = `p_v_43_safe_s`,
                        modelCoords = vec4(181.28, -1001.21, -100.1, 269.2),
                        customText = "[~b~G~w~] Crack"
                    }
                },
            }
        },
    },

skillCheck = function(stage)
    if stage == 'breakIn' then 
        return lib.skillCheck({'easy', 'easy'}, {'e', 'd'})
    end

    if stage == 'safe' then 
        local screwDriver = exports.ox_inventory:GetItemCount('screwDriver')
        if screwDriver < 1 then 
            lib.notify({description = locale('notify.noItems'), type = 'error'}) 
            return false 
        end

        local combo = {math.random(1, 359), math.random(1, 359), math.random(1, 359)}
        TriggerEvent('SafeCracker:StartMinigame', combo)
        return true
    end
end,


    policeDispatch = function(ped, coords)
        -- Police dispatch here
    end,

    isPlayerDead = function(ped, coords)
        return false
    end,

    itemFilter = { 
        pliers = true,
        bobbypins = true
    },

    craftable = {
        swap1 = 'pliers',
        swap2 = 'bobbypins',
        give = 'advancedpickkit',
        giveAmount = 15,
    },

    weapons = {
        `weapon_bat`,
        `weapon_knife`
    },

    hud = {
        x = 0.95, 
        y = 1.45, 
        width = 1.0, 
        height = 1.2,
        scale = 0.5
    },

    markercolor = { r = 26, g = 115, b = 179}, -- RGB Color picker: https://g.co/kgs/npUqed1
    markersize = vec3(1.0, 1.0, 1.0),
}