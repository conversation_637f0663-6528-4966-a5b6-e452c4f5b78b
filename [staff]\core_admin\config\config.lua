Config = {}

Config.AdminGroups = {
    'mod',
    'admin',
    'cl',
    'owner'
}

Config.TagStars = {
    ['owner'] = 4,
    ['cl'] = 3,
    ['admin'] = 2,
    ['mod'] = 1,
    ['user'] = 0,
}

Config.Colors = {
    White = 0,
    Red = 6,
    Blue = 48
}

Config.GroupColors = {
    ['owner'] = {
        name_tags_on = Config.Colors.Blue,
        name_tags_off = Config.Colors.White,
        star = Config.Colors.Blue,
        health = Config.Colors.Blue
    },
    ['cl'] = {
        name_tags_on = Config.Colors.Red,
        name_tags_off = Config.Colors.White,
        star = Config.Colors.White,
        health = Config.Colors.Red
    },
    ['admin'] = {
        name_tags_on = Config.Colors.Red,
        name_tags_off = Config.Colors.White,
        star = Config.Colors.White,
        health = Config.Colors.Red
    },
    ['mod'] = {
        name_tags_on = Config.Colors.Red,
        name_tags_off = Config.Colors.White,
        star = Config.Colors.White,
        health = Config.Colors.Red
    },
    ['user'] = {
        name_tags_on = Config.Colors.White,
        name_tags_off = Config.Colors.White,
        star = Config.Colors.White,
        health = Config.Colors.Red
    }
}

Config.NameTags = {
    Distance = 180.0,
    ShowHealth = true,
    ShowStars = true,
    ShowTalking = true,
    UpdateInterval = 5000,
    CleanupInterval = 10000,
    NameTagsActiveColor = 6,
}

Config.ToggleKey = 'F2'

Config.Messages = {
    TagsEnabled = '~g~Admin name tags enabled',
    TagsDisabled = '~r~Admin name tags disabled',
    NoPermission = '~r~You don\'t have permission to use admin name tags',
    GroupCheck = '~b~Group: %s | Admin: %s'
}
