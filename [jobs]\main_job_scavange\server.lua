local unauthorizedAttempts = 0

local lootTable = {
    {item = "scav_briefcase", chance = 1, min = 1, max = 1},
    {item = "scav_bronzecoin", chance = 30, min = 1, max = 2},
    {item = "scav_can1", chance = 80, min = 1, max = 3},
    {item = "scav_can2", chance = 75, min = 1, max = 3},
    {item = "scav_casefile", chance = 10, min = 1, max = 1},
    {item = "scav_glasses1", chance = 50, min = 1, max = 2},
    {item = "scav_glasses2", chance = 50, min = 1, max = 2},
    {item = "scav_goblet", chance = 5, min = 1, max = 1},
    {item = "scav_goldcoin", chance = 10, min = 1, max = 1},
    {item = "scav_gopro", chance = 15, min = 1, max = 1},
    {item = "scav_humanskull", chance = 5, min = 1, max = 1},
    {item = "scav_ipod", chance = 25, min = 1, max = 1},
    {item = "scav_lockbox", chance = 3, min = 1, max = 1},
    {item = "scav_oldshoe", chance = 90, min = 1, max = 2},
    {item = "scav_oldtvirus", chance = 2, min = 1, max = 1},
    {item = "scav_phone1", chance = 40, min = 1, max = 1},
    {item = "scav_scrap", chance = 70, min = 1, max = 2},
    {item = "scav_secureusb", chance = 8, min = 1, max = 1},
    {item = "scav_shell1", chance = 85, min = 1, max = 4},
    {item = "scav_shell2", chance = 80, min = 1, max = 3},
    {item = "scav_shell2_clean", chance = 60, min = 1, max = 2},
    {item = "scav_shell3", chance = 20, min = 1, max = 1},
    {item = "scav_shell3_clean", chance = 15, min = 1, max = 1},
    {item = "scav_shell4", chance = 25, min = 1, max = 1},
    {item = "scav_shell5", chance = 35, min = 1, max = 2},
    {item = "scav_shell6", chance = 40, min = 1, max = 2},
    {item = "scav_silvercoin", chance = 20, min = 1, max = 1},
    {item = "scav_zippedphone", chance = 10, min = 1, max = 1},
}

function getLootReward()
    for _, loot in ipairs(lootTable) do
        if math.random(100) <= loot.chance then
            return {
                item = loot.item,
                amount = math.random(loot.min, loot.max)
            }
        end
    end

    return nil
end

RegisterNetEvent("main_job_scavange:scuba", function()
    local src = source
    if unauthorizedAttempts > 0 then
        return
    end

    local reward = getLootReward()

    if reward then
        exports.ox_inventory:AddItem(src, reward.item, reward.amount)
        TriggerClientEvent("ox_lib:notify", src, {
            type = "success",
            icon = 'fa-solid fa-person-swimming',
            description = string.format("You found %d %s!", reward.amount, reward.item),
        })
    else
        TriggerClientEvent("ox_lib:notify", src, {
            type = "error",
            icon = 'fa-solid fa-person-swimming',
            description = "You searched but found nothing this time.",
        })
    end
end)